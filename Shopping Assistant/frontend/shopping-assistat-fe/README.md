# Shopping Assistant Frontend

A modern, responsive React application with glassmorphism design for the AI-powered Shopping Assistant.

## Features

- 🎨 **Modern Glassmorphism Design** - Beautiful glass-like UI with backdrop blur effects
- 🔐 **Authentication System** - Login and signup with JWT token management
- 💬 **Real-time Chat Interface** - Interactive chat with AI shopping assistant
- 📱 **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- ⚡ **Streaming Responses** - Simulated streaming chat responses for better UX
- 🎯 **Chat History** - View and manage previous conversations
- 🔄 **Auto-refresh Tokens** - Seamless token refresh for uninterrupted experience

## Tech Stack

- **React 19** - Latest React with hooks and modern patterns
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **React Router** - Client-side routing
- **React Hook Form** - Form handling with validation
- **Zod** - Schema validation
- **Framer Motion** - Smooth animations and transitions
- **Lucide React** - Beautiful icons
- **Axios** - HTTP client with interceptors
- **Tailwind CSS** - Utility-first CSS framework (custom implementation)

## Getting Started

### Prerequisites

- Node.js 20+
- npm or yarn
- Backend API running on `http://localhost:8000`

### Installation

1. Navigate to the frontend directory:

```bash
cd frontend/shopping-assistat-fe
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

4. Open your browser and visit `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ChatInput.tsx   # Chat input with auto-resize
│   ├── ChatMessage.tsx # Individual chat message display
│   ├── ChatHistory.tsx # Sidebar with chat history
│   ├── LoginForm.tsx   # Login form with validation
│   └── SignupForm.tsx  # Signup form with validation
├── pages/              # Page components
│   ├── AuthPage.tsx    # Authentication page
│   └── DashboardPage.tsx # Main dashboard with chat
├── hooks/              # Custom React hooks
│   └── useAuth.ts      # Authentication state management
├── services/           # API services
│   └── api.ts          # HTTP client with interceptors
├── types/              # TypeScript type definitions
│   └── index.ts        # Application types
├── utils/              # Utility functions
│   └── streaming.ts    # Streaming response utilities
├── styles/             # Global styles
│   └── globals.css     # CSS with glassmorphism effects
└── App.tsx             # Main application component
```

## Key Features Explained

### Glassmorphism Design

The application uses a custom CSS implementation with:

- Backdrop blur effects
- Semi-transparent backgrounds
- Subtle borders and shadows
- Gradient overlays
- Smooth transitions

### Authentication Flow

1. **Login/Signup** - Users can create accounts or sign in
2. **Token Management** - JWT tokens stored in localStorage
3. **Auto-refresh** - Tokens automatically refreshed when expired
4. **Protected Routes** - Unauthorized users redirected to login

### Chat Interface

- **Real-time Updates** - Messages appear instantly
- **Streaming Simulation** - AI responses appear word by word
- **Chat History** - Previous conversations saved and accessible
- **Responsive Design** - Works on all screen sizes
- **Auto-scroll** - Automatically scrolls to latest messages

### API Integration

The frontend communicates with the backend API:

- **Base URL**: `http://localhost:8000`
- **Authentication**: Bearer token in headers
- **Error Handling**: Graceful error messages
- **Loading States**: Visual feedback during requests

## Customization

### Colors and Themes

Edit `src/styles/globals.css` to customize:

- Color palette
- Glassmorphism effects
- Animations
- Typography

### API Configuration

Update `src/services/api.ts` to change:

- API base URL
- Request/response interceptors
- Error handling

## Development

### Adding New Components

1. Create component in `src/components/`
2. Use TypeScript interfaces for props
3. Include glassmorphism classes
4. Add animations with Framer Motion

### Adding New Pages

1. Create page in `src/pages/`
2. Add route in `src/App.tsx`
3. Implement authentication guards if needed

### Styling Guidelines

- Use CSS custom properties for colors
- Apply glassmorphism classes: `.glass`, `.glass-hover`
- Use utility classes for layout and spacing
- Maintain consistent border radius and shadows

## Troubleshooting

### Common Issues

1. **CORS Errors** - Ensure backend allows requests from frontend origin
2. **Token Issues** - Check localStorage and API token handling
3. **Build Errors** - Verify TypeScript types and dependencies

### Performance

- Components are optimized with React.memo where appropriate
- Images and assets are optimized
- Code splitting implemented for better loading

## Contributing

1. Follow TypeScript best practices
2. Use consistent naming conventions
3. Add proper error handling
4. Test on multiple screen sizes
5. Maintain glassmorphism design consistency

## License

This project is part of the Shopping Assistant application.
