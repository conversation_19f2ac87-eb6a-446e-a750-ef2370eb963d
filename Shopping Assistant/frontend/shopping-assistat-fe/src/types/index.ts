export interface User {
  id: string;
  email: string;
  username: string;
  is_active: boolean;
  is_super_admin: boolean;
  created_at: string;
  updated_at: string | null;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  email: string;
  username: string;
  password: string;
}

export interface AuthResponse {
  token: {
    access_token: string;
    token_type: string;
    refresh_token: string;
  };
  user_data: User;
  user_roles: Array<{
    role: string;
    resource_id: string | null;
    resource_type: string;
  }>;
}

export interface Message {
  message_id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  attachments?: any[];
}

export interface Chat {
  chat_id: string;
  title: string;
  messages: Message[];
  created_at: string;
  updated_at: string;
}

export interface ChatRequest {
  chat_id?: string;
  messages: {
    role: "user" | "assistant";
    content: string;
  }[];
}

export interface ChatResponse {
  chat_id: string;
  title: string;
  messages: Message[];
  created_at: string;
  updated_at: string;
}

export interface ApiError {
  detail: string;
  status_code: number;
}
