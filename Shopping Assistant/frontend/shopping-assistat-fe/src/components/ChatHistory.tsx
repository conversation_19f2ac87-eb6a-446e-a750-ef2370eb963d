import React from "react";
import { motion } from "framer-motion";
import { MessageSquare, Trash2, Plus } from "lucide-react";
import type { Chat } from "../types";

interface ChatHistoryProps {
  chats: Chat[];
  currentChatId?: string;
  onSelectChat: (chatId: string) => void;
  onNewChat: () => void;
  onDeleteChat: (chatId: string) => void;
}

export const ChatHistory: React.FC<ChatHistoryProps> = ({
  chats,
  currentChatId,
  onSelectChat,
  onNewChat,
  onDeleteChat,
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "Today";
    } else if (diffDays === 2) {
      return "Yesterday";
    } else if (diffDays <= 7) {
      return date.toLocaleDateString("en-US", { weekday: "long" });
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    }
  };

  const truncateTitle = (title: string, maxLength: number = 30) => {
    return title.length > maxLength
      ? title.substring(0, maxLength) + "..."
      : title;
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-glass-border">
        <button onClick={onNewChat} className="btn btn-primary w-full">
          <Plus className="w-4 h-4" />
          New Chat
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-2">
        {chats.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="w-12 h-12 text-text-muted mx-auto mb-4" />
            <p className="text-text-muted text-sm">No chat history yet</p>
            <p className="text-text-muted text-xs mt-1">
              Start a new conversation
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {chats.map((chat, index) => (
              <motion.div
                key={chat.chat_id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`group relative cursor-pointer rounded-lg p-3 transition-all ${
                  currentChatId === chat.chat_id
                    ? "glass bg-surface-hover border border-primary-color"
                    : "hover:glass hover:bg-surface-hover"
                }`}
                onClick={() => onSelectChat(chat.chat_id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-text-primary truncate">
                      {truncateTitle(chat.title || "New Chat")}
                    </h3>
                    <p className="text-xs text-text-muted mt-1">
                      {formatDate(chat.updated_at)}
                    </p>
                  </div>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteChat(chat.chat_id);
                    }}
                    className="opacity-0 group-hover:opacity-100 p-1 text-text-muted hover:text-error-color transition-all"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
