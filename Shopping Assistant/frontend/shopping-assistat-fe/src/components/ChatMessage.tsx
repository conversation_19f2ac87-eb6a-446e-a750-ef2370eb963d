import React from "react";
import { motion } from "framer-motion";
import { User, <PERSON><PERSON> } from "lucide-react";
import type { Message } from "../types";

interface ChatMessageProps {
  message: Message;
  isLastMessage?: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isLastMessage = false,
}) => {
  const isUser = message.role === "user";

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex gap-4 ${isUser ? "justify-end" : "justify-start"} mb-6`}
    >
      {!isUser && (
        <div className="flex-shrink-0">
          <div className="glass p-2 rounded-full">
            <Bot className="w-5 h-5 text-accent-color" />
          </div>
        </div>
      )}

      <div className={`max-w-[70%] ${isUser ? "order-first" : ""}`}>
        <div
          className={`p-4 rounded-2xl ${
            isUser
              ? "glass bg-gradient-to-r from-primary-color to-secondary-color text-white"
              : "glass border border-glass-border"
          }`}
        >
          <div className="flex items-start gap-3">
            {isUser && (
              <div className="flex-shrink-0">
                <div className="glass p-1 rounded-full">
                  <User className="w-4 h-4 text-white" />
                </div>
              </div>
            )}

            <div className="flex-1">
              <p className="text-sm leading-relaxed whitespace-pre-wrap">
                {message.content}
              </p>
            </div>
          </div>
        </div>

        <div
          className={`mt-2 text-xs text-text-muted ${
            isUser ? "text-right" : "text-left"
          }`}
        >
          {new Date(message.timestamp).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>

      {isUser && (
        <div className="flex-shrink-0">
          <div className="glass p-2 rounded-full">
            <User className="w-5 h-5 text-primary-color" />
          </div>
        </div>
      )}
    </motion.div>
  );
};
