// Utility functions for handling streaming responses
// In a real implementation, you would use Server-Sent Events or WebSocket

export const simulateStreamingResponse = async (
  message: string,
  onChunk: (chunk: string) => void,
  onComplete: (fullResponse: string) => void
) => {
  // Simulate streaming by sending chunks with delays
  const words = message.split(" ");
  let fullResponse = "";

  for (let i = 0; i < words.length; i++) {
    const chunk = words[i] + (i < words.length - 1 ? " " : "");
    fullResponse += chunk;
    onChunk(fullResponse);

    // Random delay between 50-150ms to simulate real streaming
    await new Promise((resolve) =>
      setTimeout(resolve, Math.random() * 100 + 50)
    );
  }

  onComplete(fullResponse);
};

export const createEventSource = (url: string) => {
  // This would be used for real Server-Sent Events implementation
  return new EventSource(url);
};

export const createWebSocket = (url: string) => {
  // This would be used for real WebSocket implementation
  return new WebSocket(url);
};
