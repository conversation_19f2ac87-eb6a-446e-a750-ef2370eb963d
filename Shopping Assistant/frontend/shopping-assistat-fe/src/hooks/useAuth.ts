import {
  useState,
  useEffect,
  createContext,
  useContext,
  createElement,
} from "react";
import type { User, AuthResponse } from "../types";
import { apiService } from "../services/api";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem("access_token");
      if (token) {
        try {
          const currentUser = await apiService.getCurrentUser();
          setUser(currentUser);
        } catch (error) {
          console.error("Auth initialization error:", error);
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          setUser(null);
        }
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response: AuthResponse = await apiService.login({
        email,
        password,
      });
      localStorage.setItem("access_token", response.token.access_token);
      localStorage.setItem("refresh_token", response.token.refresh_token);
      setUser(response.user_data);
      console.log("Login successful, user set:", response.user_data);
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const signup = async (email: string, username: string, password: string) => {
    try {
      const response: AuthResponse = await apiService.signup({
        email,
        username,
        password,
      });
      localStorage.setItem("access_token", response.token.access_token);
      localStorage.setItem("refresh_token", response.token.refresh_token);
      setUser(response.user_data);
      console.log("Signup successful, user set:", response.user_data);
    } catch (error) {
      console.error("Signup error:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    signup,
    logout,
    isAuthenticated: !!user,
  };

  return createElement(AuthContext.Provider, { value }, children);
};
