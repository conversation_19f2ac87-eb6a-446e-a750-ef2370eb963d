import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { LogOut, Menu, X, ShoppingBag, Bot } from "lucide-react";
import { useAuth } from "../hooks/useAuth";
import { ChatMessage } from "../components/ChatMessage";
import { ChatInput } from "../components/ChatInput";
import { ChatHistory } from "../components/ChatHistory";
import { apiService } from "../services/api";
import type { Chat, Message, ChatRequest } from "../types";

export const DashboardPage: React.FC = () => {
  const { user, logout } = useAuth();
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [streamingMessage, setStreamingMessage] = useState("");

  // Load chat history on mount
  useEffect(() => {
    loadChatHistory();
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [currentChat?.messages, streamingMessage]);

  const loadChatHistory = async () => {
    try {
      const chatHistory = await apiService.getChatHistory();
      setChats(chatHistory);
    } catch (error) {
      console.error("Failed to load chat history:", error);
    }
  };

  const handleNewChat = () => {
    setCurrentChat(null);
    setStreamingMessage("");
    setSidebarOpen(false);
  };

  const handleSelectChat = async (chatId: string) => {
    try {
      const chat = await apiService.getChatById(chatId);
      setCurrentChat(chat);
      setStreamingMessage("");
      setSidebarOpen(false);
    } catch (error) {
      console.error("Failed to load chat:", error);
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    try {
      await apiService.deleteChat(chatId);
      setChats(chats.filter((chat) => chat.chat_id !== chatId));
      if (currentChat?.chat_id === chatId) {
        setCurrentChat(null);
      }
    } catch (error) {
      console.error("Failed to delete chat:", error);
    }
  };

  const handleSendMessage = async (messageContent: string) => {
    if (!messageContent.trim()) return;

    setIsLoading(true);
    setIsStreaming(true);
    setStreamingMessage("");

    const userMessage: Message = {
      message_id: Date.now().toString(),
      role: "user",
      content: messageContent,
      timestamp: new Date().toISOString(),
    };

    // Add user message to current chat
    const updatedChat = currentChat
      ? {
          ...currentChat,
          messages: [...currentChat.messages, userMessage],
        }
      : {
          chat_id: Date.now().toString(),
          title: "New Chat",
          messages: [userMessage],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

    setCurrentChat(updatedChat);

    try {
      // Prepare chat request
      const chatRequest: ChatRequest = {
        chat_id: currentChat?.chat_id,
        messages: [
          ...(currentChat?.messages || []),
          { role: "user", content: messageContent },
        ],
      };

      // Simulate streaming response (in a real app, you'd use Server-Sent Events or WebSocket)
      const response = await apiService.sendMessage(chatRequest);

      // Update chat with response
      setCurrentChat(response);

      // Update chat history
      const updatedChats = chats.filter(
        (chat) => chat.chat_id !== response.chat_id
      );
      setChats([response, ...updatedChats]);
    } catch (error) {
      console.error("Failed to send message:", error);
      // Add error message
      const errorMessage: Message = {
        message_id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "Sorry, I encountered an error. Please try again.",
        timestamp: new Date().toISOString(),
      };
      setCurrentChat({
        ...updatedChat,
        messages: [...updatedChat.messages, errorMessage],
      });
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setStreamingMessage("");
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const allMessages = [
    ...(currentChat?.messages || []),
    ...(streamingMessage
      ? [
          {
            message_id: "streaming",
            role: "assistant" as const,
            content: streamingMessage,
            timestamp: new Date().toISOString(),
          },
        ]
      : []),
  ];

  return (
    <div className="h-screen flex bg-background">
      {/* Sidebar */}
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: sidebarOpen ? 0 : -300 }}
        className="fixed md:relative z-20 w-80 h-full glass border-r border-glass-border"
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-glass-border">
            <div className="flex items-center gap-3 mb-4">
              <div className="glass p-2 rounded-lg">
                <ShoppingBag className="w-6 h-6 text-gradient" />
              </div>
              <div>
                <h1 className="font-semibold text-text-primary">
                  Shopping Assistant
                </h1>
                <p className="text-xs text-text-muted">
                  Welcome, {user?.username}
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="btn btn-ghost w-full text-sm"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </button>
          </div>

          {/* Chat History */}
          <ChatHistory
            chats={chats}
            currentChatId={currentChat?.chat_id}
            onSelectChat={handleSelectChat}
            onNewChat={handleNewChat}
            onDeleteChat={handleDeleteChat}
          />
        </div>
      </motion.div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Mobile Header */}
        <div className="md:hidden p-4 border-b border-glass-border glass">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 glass rounded-lg"
            >
              {sidebarOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </button>
            <div className="flex items-center gap-2">
              <Bot className="w-5 h-5 text-gradient" />
              <span className="font-semibold">Shopping Assistant</span>
            </div>
            <div className="w-10"></div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto p-4">
          {allMessages.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="glass p-6 rounded-2xl mb-6">
                  <Bot className="w-16 h-16 text-gradient mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-2">
                    Welcome to Shopping Assistant
                  </h2>
                  <p className="text-text-secondary mb-4">
                    I can help you find products, compare prices, and make
                    shopping recommendations.
                  </p>
                  <div className="space-y-2 text-sm text-text-muted">
                    <p>• "What headphones do you recommend under $100?"</p>
                    <p>• "Show me wireless charging pads"</p>
                    <p>• "Compare fitness watches"</p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {allMessages.map((message, index) => (
                <ChatMessage
                  key={message.message_id}
                  message={message}
                  isLastMessage={index === allMessages.length - 1}
                />
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Chat Input */}
        <div className="p-4 border-t border-glass-border">
          <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />
        </div>
      </div>

      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-10 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};
