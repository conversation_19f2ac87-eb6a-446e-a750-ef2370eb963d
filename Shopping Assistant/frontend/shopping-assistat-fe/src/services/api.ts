import axios from "axios";
import type { AxiosInstance, AxiosResponse } from "axios";
import type {
  LoginRequest,
  SignupRequest,
  AuthResponse,
  ChatRequest,
  ChatResponse,
  User,
  ApiError,
} from "../types";

class ApiService {
  private api: AxiosInstance;
  private baseURL = "http://localhost:8000";

  constructor() {
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Add request interceptor to include auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem("access_token");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor to handle token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = localStorage.getItem("refresh_token");
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              localStorage.setItem("access_token", response.token.access_token);
              localStorage.setItem(
                "refresh_token",
                response.token.refresh_token
              );

              originalRequest.headers.Authorization = `Bearer ${response.token.access_token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            localStorage.removeItem("access_token");
            localStorage.removeItem("refresh_token");
            window.location.href = "/auth";
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post(
      "/auth/login",
      credentials
    );
    return response.data;
  }

  async signup(userData: SignupRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post(
      "/auth/signup",
      userData
    );
    return response.data;
  }

  async logout(): Promise<void> {
    await this.api.post("/auth/logout");
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post(
      "/auth/refresh",
      {
        refresh_token: refreshToken,
      }
    );
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get("/auth/me");
    return response.data;
  }

  // Chat endpoints
  async sendMessage(chatRequest: ChatRequest): Promise<ChatResponse> {
    const response: AxiosResponse<ChatResponse> = await this.api.post(
      "/chat",
      chatRequest
    );
    return response.data;
  }

  async getChatHistory(): Promise<ChatResponse[]> {
    const response: AxiosResponse<ChatResponse[]> = await this.api.get(
      "/chat/history"
    );
    return response.data;
  }

  async getChatById(chatId: string): Promise<ChatResponse> {
    const response: AxiosResponse<ChatResponse> = await this.api.get(
      `/chat/${chatId}`
    );
    return response.data;
  }

  async deleteChat(chatId: string): Promise<void> {
    await this.api.delete(`/chat/${chatId}`);
  }
}

export const apiService = new ApiService();
