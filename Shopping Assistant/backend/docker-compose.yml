services:
  # ---------------------------------
  # PostgreSQL Service
  # ---------------------------------
  postgres:
    image: postgres:16.6-alpine3.20
    env_file:
      - .env.db
    ports:
      - "5432:5432" # Host port : Container port (change as needed)
    networks:
      - llama-net
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      # Use CMD-SHELL to allow environment variable expansion. Exit 1 if pg_isready fails.
      test:
        ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
  # ---------------------------------
  # Redis Service
  # ---------------------------------
  redis:
    image: redis:7.4.2-alpine3.21
    restart: always

    networks:
      - llama-net
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    volumes:
      - cache:/data
  # ---------------------------------
  # FastAPI Service
  # ---------------------------------
  fastapi:
    build: .
    volumes:
      - .:/app
      - ./uploads:/app/uploads # Mount current directory into /app in the container
    ports:
      - "8000:8000" # Expose FastAPI on port 8000
    env_file:
      - .env # Main app env (JWT config, etc.)
    networks:
      - llama-net
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    command:
      [
        "uvicorn",
        "app.fastapi_app:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload",
      ]

networks:
  llama-net:
    driver: bridge

volumes:
  postgres_data:
  cache:
