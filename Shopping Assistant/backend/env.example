# -----------------------------------------------------------------------------
# Copy this file to .env and fill in the real values.
# -----------------------------------------------------------------------------

# -----------------------------------------------------------------------------
# PostgreSQL Configuration
# -----------------------------------------------------------------------------
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_HOST=your_postgres_host
POSTGRES_PORT=5432
POSTGRES_DB=your_database_name

# -----------------------------------------------------------------------------
# Database URLs
# -----------------------------------------------------------------------------
DATABASE_URL=postgresql+asyncpg://your_postgres_user:your_postgres_password@your_postgres_host:5432/your_database_name
SYNC_DB_URL=******************************************************************************/your_database_name

# -----------------------------------------------------------------------------
# JWT / Auth Configuration
# -----------------------------------------------------------------------------
SECRET_KEY=your_long_random_secret_key
REFRESH_SECRET_KEY=your_long_random_refresh_key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# -----------------------------------------------------------------------------
# Redis Configuration
# -----------------------------------------------------------------------------
REDIS_HOST=your_redis_host
REDIS_PORT=6379

# -----------------------------------------------------------------------------
# Ollama LLM Configuration
# -----------------------------------------------------------------------------
OLLAMA_HOST=your_ollama_host
OLLAMA_PORT=11434
OLLAMA_URL=http://${OLLAMA_HOST}:${OLLAMA_PORT}
OLLAMA_MODEL=llama3.2
OLLAMA_CHAT_URL=""

# -----------------------------------------------------------------------------
# OpenAI Configuration
# -----------------------------------------------------------------------------
OPENAI_API_KEY=your_openai_api_key

# -----------------------------------------------------------------------------
# SMTP / Email Configuration
# -----------------------------------------------------------------------------
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# -----------------------------------------------------------------------------
# OTP / One‑Time‑Password Configuration
# -----------------------------------------------------------------------------
OTP_EXPIRY_TIME_IN_SECONDS=600    # default 10 minutes
OTP_COUNTER_TIME_SECONDS=120      # default 2 minutes
NUMBER_OF_OTP_WITHIN_COUNTER_TIME=3
OTP_RESTRICTION_TIME=900          # default 15 minutes

# -----------------------------------------------------------------------------
# Qdrant Vector Store Configuration
# -----------------------------------------------------------------------------
QDRANT_HOST=your_qdrant_host
QDRANT_PORT=6333
QDRANT_URL=http://your_qdrant_host:6333
QDRANT_COLLECTION_PREFIX=your_collection_prefix

# -----------------------------------------------------------------------------
# Storage Configuration
# -----------------------------------------------------------------------------
STORAGE_PROVIDER=local    # local
UPLOAD_DIR=/app/uploads

#S3 Configuration (uncomment if STORAGE_PROVIDER=s3)
S3_ACCESS_KEY_ID=your_access_key
S3_SECRET_ACCESS_KEY=your_secret_key

# -----------------------------------------------------------------------------
# Celery Configuration
# -----------------------------------------------------------------------------
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/1

# -----------------------------------------------------------------------------
# Encryption Key
# -----------------------------------------------------------------------------
ENCRYPTION_KEY=your_32_byte_base64_key

# -----------------------------------------------------------------------------
# Embedding / Chunking Settings
# -----------------------------------------------------------------------------
DEFAULT_CHUNK_SIZE=200    # default token chunk size
CHUNK_OVERLAP=20          # default token overlap between chunks

FRONTEND_URL=https://front-end-url.com

# -----------------------------------------------------------------------------
# AWS Bedrock model integration config
# -----------------------------------------------------------------------------

# Custom endpoint URL
BEDROCK_ENDPOINT_URL = bedrock-endpoint
BEDROCK_AWS_REGION=bedrock-aws-region
# Enable or Disable different Model providers
ENABLE_OLLAMA_CHAT_MODEL=true
ENABLE_OPENAI_CHAT_MODEL=true
ENABLE_BEDROCK_CHAT_MODEL=true