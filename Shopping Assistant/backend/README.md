# Shopping Assistant - FastAPI Project

## Overview

Shopping Assistant is an AI-powered conversational platform built using FastAPI, Docker, and Alembic for handling migrations. It provides chat functionality and user authentication for shopping-related queries and assistance. The project is designed to run with Docker and Docker Compose.

## Features

- **User Authentication**: Sign up, login, and logout functionality with JWT tokens
- **AI Chat**: Conversational AI for shopping assistance and product recommendations
- **Secure API**: JWT-based authentication and authorization
- **Password Reset**: Secure password reset functionality

## Tech Stack

- **Backend Framework**: [FastAPI](https://fastapi.tiangolo.com/)
- **Database**: Managed with Alembic migrations.
- **Containerization**: Docker and Docker Compose for easy setup and deployment.
- **Dependency Management**: Poetry for Python package management.
- **Environment Management**: `.env` files for environment variable management.

## Installation

Clone this repository:

```bash
git clone <repository_url>
cd shopping-assistant
```

## Configure environment variables

Create `.env` and `.env.db` files by referring to the `env.example` and `env.db.example` files:

```bash
cp env.example .env
cp env.db.example .env.db
```

Start the project with Docker Compose:

```bash
docker compose up --build
```

## Running the Alembic Migrations

Apply migrations using Alembic:

```bash
docker compose exec fastapi alembic upgrade head
```

To create a new migration:

```bash
docker compose exec fastapi alembic revision --autogenerate -m "Migration message"
```

## API Endpoints

### Authentication

- `POST /auth/signup` - User registration
- `POST /auth/login` - User login (returns JWT tokens directly)
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password with token

### User Management

- `GET /users/{user_id}` - Get user details
- `PUT /users/{user_id}` - Update user
- `POST /users/{user_id}/suspend` - Suspend user
- `POST /users/{user_id}/reactivate` - Reactivate user
- `DELETE /users/{user_id}` - Delete user
- `POST /users/invite` - Invite new user
- `GET /users/list/all` - List all users

### Chat

- `POST /chat/` - Create new chat
- `GET /chat/` - List user chats
- `POST /chat/{chat_id}/messages` - Send message
- `GET /chat/{chat_id}/messages` - Get chat messages

## Stopping the Application

To stop the application, run:

```bash
docker compose down
```
