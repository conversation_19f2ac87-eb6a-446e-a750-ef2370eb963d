# Environment variables
.env
.env.db
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/
*.iml

# VSCode
.vscode/

# macOS
.DS_Store

# Database files
*.db
*.sqlite3

# Logs
*.log

# Virtual environments
venv/
env/
ENV/
.venv/

# Docker
docker-compose.override.yml

__pycache__
*.pyc
*.pyo
*.pyd
env
venv
.venv
.git