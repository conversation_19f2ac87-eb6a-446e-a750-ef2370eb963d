# Start with a builder image for dependency installation
FROM python:3.13.1-alpine3.21 AS builder

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache curl

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python3 -
ENV PATH="/root/.local/bin:${PATH}"

# Copy dependency files
COPY pyproject.toml ./

# Configure Poetry and install dependencies
RUN poetry config virtualenvs.create false && poetry install --no-root

# Start a minimal runtime image
FROM python:3.13.1-alpine3.21 AS runtime

# Set the working directory
WORKDIR /app

# Create a non-root user
RUN adduser -D shoppinguser

# Ensure the uploads directory exists and has correct permissions
RUN mkdir -p /app/uploads && chown -R shoppinguser:shoppinguser /app/uploads && chmod -R 755 /app/uploads

# Switch to the non-root user
USER shoppinguser

# Copy installed dependencies from builder stage
COPY --from=builder /usr/local/lib/python3.13/site-packages /usr/local/lib/python3.13/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY --chown=shoppinguser:shoppinguser . .

# Expose the port
EXPOSE 8000

# Command to run the FastAPI app
CMD ["uvicorn", "app.fastapi_app:app", "--host", "0.0.0.0", "--port", "8000"]
