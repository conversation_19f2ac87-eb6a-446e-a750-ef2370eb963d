[tool.poetry]
name = "shopping-assistant"
version = "0.1.0"
description = "Shopping Assistant is an AI-powered conversational platform designed to help users with shopping queries, product recommendations, and purchase assistance"
authors = ["sreesankar12 <<EMAIL>>", "dany <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.12,<4.0"
fastapi = ">=0.115.7,<0.116.0"
uvicorn = ">=0.34.0,<0.35.0"
pydantic = { version = ">=2.10.6,<3.0.0", extras = ["email"] }
pydantic-settings = ">=2.7.1,<3.0.0"
sqlalchemy = ">=2.0.37,<3.0.0"
pymysql = ">=1.1.1,<2.0.0"
cryptography = ">=44.0.0,<45.0.0"
alembic = ">=1.14.1,<2.0.0"
psycopg2-binary = ">=2.9.10,<3.0.0"
asyncpg = ">=0.30.0,<0.31.0"
passlib = { version = ">=1.7.4,<2.0.0", extras = ["bcrypt"] }
pyjwt = "^2.10.1"
redis = "^5.2.1"
pytest = "^8.3.4"
pytest-asyncio = "^0.25.3"
httpx = "^0.28.1"
coverage = "^7.6.12"
requests = "^2.32.3"
langchain = "^0.3.19"
langchain-community = "^0.3.18"
openai = "^1.65.1"
langchain-openai = "^0.3.7"
aiofiles = "^24.1.0"
python-multipart = "^0.0.20"


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
