import asyncio
import logging
import os
import sys
from pathlib import Path

from alembic import context
from logging.config import fileConfig
from sqlalchemy.ext.asyncio import create_async_engine

# Import the Base model to obtain target metadata for migrations
from app.models import Base

# ------------------------------------------------------------------------------
# Setup path and logging
# ------------------------------------------------------------------------------

# Add the project base directory to sys.path for module resolution
BASE_PATH = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_PATH))

# Retrieve Alembic configuration object
config = context.config

# Configure logging from the config file, if available
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# ------------------------------------------------------------------------------
# Database Configuration
# ------------------------------------------------------------------------------

# Retrieve the DATABASE_URL from the environment
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable is not set.")

# Create an asynchronous engine with SQLAlchemy 2.0 style support
async_engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # Set to False in production environments
    future=True,
)

# ------------------------------------------------------------------------------
# Target Metadata
# ------------------------------------------------------------------------------

# Set the target metadata for Alembic autogenerate support
target_metadata = Base.metadata

# ------------------------------------------------------------------------------
# Migration Routines
# ------------------------------------------------------------------------------

def run_migrations_offline() -> None:
    """
    Run migrations in 'offline' mode.

    This mode configures the context with just a URL and does not require an Engine.
    """
    # Use the URL from Alembic's configuration or fall back to the environment variable
    url = config.get_main_option("sqlalchemy.url") or DATABASE_URL
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_migrations(sync_connection):
    """
    Perform the migration run in 'online' mode.

    This function is executed in a synchronous context by SQLAlchemy's run_sync method.
    """
    context.configure(
        connection=sync_connection,
        target_metadata=target_metadata,
    )
    with context.begin_transaction():
        context.run_migrations()


async def run_migrations_online():
    """
    Run migrations in 'online' mode using an asynchronous connection.
    """
    async with async_engine.connect() as connection:
        # Run the synchronous migration function within the async connection context
        await connection.run_sync(do_migrations)


# ------------------------------------------------------------------------------
# Main Execution Block
# ------------------------------------------------------------------------------

if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online())
