from typing import List
from uuid import UUID
from datetime import datetime

from pydantic import BaseModel


class MessageSchema(BaseModel):
    role: str
    content: str


class MessageResponse(BaseModel):
    message_id: str
    role: str
    content: str
    timestamp: datetime


class ChatRequest(BaseModel):
    messages: list[MessageSchema]
    chat_id: UUID | None = None  # Optional, to continue an existing chat


class UpdateTitleRequest(BaseModel):
    title: str


class ChatResponse(BaseModel):
    chat_id: str
    title: str
    created_at: datetime
    updated_at: datetime | None


class ChatModelConfig(BaseModel):
    name: str
    description: str