import logging
from datetime import datetime, timedelta
from sqlalchemy.future import select
from typing import List, Optional, Literal
from uuid import UUID

import jwt
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jwt import PyJWTError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.redis_client import get_redis
from app.database.database import get_db
from app.models.enums import ResourceType, PermissionLevel, RoleType
from app.models.user import User
from app.schemas.user import UserCreate, UserResponse, UserUpdate
from app.schemas.user import (
    UserSignup,
    UserLogin,
    ForgotPasswordRequest,
    ResetPasswordRequest,
    LoginResponse,
    RefreshRequest
)
from app.services.user import create_user, search_users, update_user, suspend_user, reactivate_user, delete_user, \
    get_user_details, list_all_users
from app.services.user import (signup_user, login_user, forgot_password, reset_password)
from app.utils.auth_utils import create_access_token, decode_token
from app.utils.auth_utils import get_current_user

SECRET_KEY = settings.SECRET_KEY
REFRESH_SECRET_KEY = settings.REFRESH_SECRET_KEY
ALGORITHM = settings.ALGORITHM

router = APIRouter(
    prefix="/auth",
    tags=["auth"]
)

user_router = APIRouter(prefix="/users", tags=["users"])
log = logging.getLogger(__name__)
log.setLevel("INFO")

bearer_security = HTTPBearer(auto_error=False)

@router.post("/signup", response_model=UserResponse)
async def signup(user_signup: UserSignup, db: AsyncSession = Depends(get_db)):
    """
    Create a new user account. This endpoint will create a new user record and
    an associated authentication record (with a hashed password).
    """
    try:
        new_user = await signup_user(db, user_signup)
        # Construct a response schema from the created user
        return UserResponse(
            id=new_user.id,
            email=new_user.email,
            username=new_user.username,
            created_at=new_user.created_at,
            is_super_admin=new_user.is_super_admin
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=LoginResponse)
async def login(user_login: UserLogin, db: AsyncSession = Depends(get_db)):
    """
    Authenticate an existing user. If the provided credentials are valid,
    return JWT tokens directly.
    """
    return await login_user(db, user_login)

@router.post(
    "/forgot-password",
    summary="Forgot Password",
    description="Initiates the password reset process by generating a short-lived JWT token, "
                "which will be sent to the user's email for resetting their password.",
    responses={
        200: {"description": "Password reset instructions have been successfully initiated"},
        400: {"description": "Bad Request. The provided email is invalid or an error occurred during processing."}
    }
)
async def forgot_password_route(data: ForgotPasswordRequest, db: AsyncSession = Depends(get_db)):
    """
    Handle forgot password request by generating a reset token.
    """
    try:
        reset_token = await forgot_password(db, data)
        return {"reset_token": reset_token, "message": "Reset token generated successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post(
    "/reset-password",
    summary="Reset Password",
    description="Resets the user's password using the provided token and new password.",
    responses={
        200: {"description": "Password was successfully updated."},
        400: {"description": "Bad Request. Something went wrong during the password reset process."}
    }
)
async def reset_password_route(data: ResetPasswordRequest, db: AsyncSession = Depends(get_db)):
    """
    Reset password using a valid reset token.
    """
    try:
        await reset_password(db, data)
        return {"message": "Password reset successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post(
    "/logout",
    summary="User Logout",
    description="Invalidate the current JWT token by blacklisting it until it expires.",
    responses={200: {"description": "Successfully logged out"}}
)
async def logout(
    creds: HTTPAuthorizationCredentials = Depends(bearer_security),
    redis=Depends(get_redis)
):
    """
    Logout the current user by blacklisting their JWT token.
    """
    try:
        if creds is None:
            raise HTTPException(status_code=403, detail="Not authenticated")

        token = creds.credentials
        # Blacklist the token
        redis.setex(f"blacklist:{token}", settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60, "blacklisted")
        
        return {"message": "Successfully logged out"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Logout failed: {str(e)}"
        )

@router.post("/refresh",
             summary="Refresh Token",
             description="Refresh the JWT token."
             )
async def refresh_token(req: RefreshRequest, db: AsyncSession = Depends(get_db)):
    """
    Refresh the JWT token using a valid refresh token.
    """
    try:
        # Decode the refresh token
        payload = decode_token(req.refresh_token, REFRESH_SECRET_KEY)
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Get user from database
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        # Create new access token
        access_token = create_access_token(data={"sub": str(user.id)})
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=req.refresh_token,
            user=UserResponse(
                id=user.id,
                email=user.email,
                username=user.username,
                created_at=user.created_at,
                is_super_admin=user.is_super_admin
            )
        )
    except PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    Get current user information.
    """
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        created_at=current_user.created_at,
        is_super_admin=current_user.is_super_admin
    )

################################
# User management routes
################################

@user_router.post("", status_code=status.HTTP_201_CREATED, response_model=UserResponse)
async def create_user_route(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new user."""
    return await create_user(db, user_data, current_user)

@user_router.get("/{user_id}")
async def get_user_route(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific user by ID."""
    return await get_user_details(db, user_id)

@user_router.get("", response_model=List[UserResponse])
async def search_users_route(
    query: str = Query(..., description="Search by email or username"),
    skip: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Search users by email or username."""
    return await search_users(db, query, skip, limit, current_user.id)

@user_router.put("/{user_id}", response_model=UserResponse)
async def update_user_route(
    user_id: UUID,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a user."""
    return await update_user(db, user_id, user_data, current_user.id)

@user_router.post("/{user_id}/suspend", response_model=UserResponse)
async def suspend_user_route(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Suspend a user."""
    return await suspend_user(db, user_id, current_user.id)

@user_router.post("/{user_id}/reactivate", response_model=UserResponse)
async def reactivate_user_route(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Reactivate a suspended user."""
    return await reactivate_user(db, user_id, current_user.id)

@user_router.delete("/{user_id}", status_code=status.HTTP_200_OK)
async def delete_user_route(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a user."""
    await delete_user(db, user_id, current_user.id)
    return {"message": "User deleted successfully"}

@user_router.get("/list/all", response_model=List[UserResponse])
async def list_users_by_client(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List all users."""
    return await list_all_users(db)
