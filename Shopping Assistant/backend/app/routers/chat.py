from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.constants import ErrorMessages
from app.database.database import get_db
from app.models import User
from app.schemas.chat import (
    ChatModelConfig,
    ChatRequest,
    ChatResponse,
    MessageResponse,
    UpdateTitleRequest,
)
from app.services.chat import ChatService
from app.utils.auth_utils import get_current_user
from app.utils.chat_utils import get_chat_by_id
from app.utils.model_configs import get_chat_model_configs
from app.utils.user_utils import get_user_by_id

chat_router = APIRouter(prefix="/chat", tags=["Chat"])

# Create a single instance of ChatService
chat_service = ChatService()

################################
# Chat CRUD routes
################################


@chat_router.post("")
async def chat(
    request: ChatRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Handle chat requests and return responses."""
    return await chat_service.chat(db, request, current_user)


@chat_router.put("/{chat_id}/title", response_model=ChatResponse)
async def update_chat_title(
    chat_id: UUID,
    request: UpdateTitleRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    chat = await get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    if chat.user_id != current_user.id:
        raise HTTPException(status_code=403, detail=ErrorMessages.FORBIDDEN)
    return await ChatService.update_chat_title(db, chat_id, request)


@chat_router.get("/{chat_id}/history", response_model=list[MessageResponse])
async def get_chat_history(
    chat_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    # Fetch the chat and check if it belongs to the current user
    chat = await get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    if chat.user_id != current_user.id:
        raise HTTPException(status_code=403, detail=ErrorMessages.FORBIDDEN)
    return await ChatService.get_chat_history(db, chat_id)


@chat_router.get("/{user_id}/user-chats", response_model=list[ChatResponse])
async def get_user_chats(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    user = await get_user_by_id(db, user_id)
    if user.id != current_user.id:
        raise HTTPException(status_code=403, detail=ErrorMessages.FORBIDDEN)

    return await ChatService.get_user_chats(db, user_id)


@chat_router.get("/models", response_model=list[ChatModelConfig])
async def get_chat_models(current_user: User = Depends(get_current_user)):
    """Fetch the list of chat model configurations."""
    return get_chat_model_configs()


@chat_router.get("/{chat_id}", response_model=ChatResponse)
async def get_chat_details(
    chat_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    chat = await get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    if chat.user_id != current_user.id:
        raise HTTPException(status_code=403, detail=ErrorMessages.FORBIDDEN)
    return await ChatService.get_chat_details(db, chat_id)


@chat_router.delete("/{chat_id}")
async def delete_chat(
    chat_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    chat = await get_chat_by_id(db, chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    if chat.user_id != current_user.id:
        raise HTTPException(status_code=403, detail=ErrorMessages.FORBIDDEN)
    return await ChatService.delete_chat(db, chat_id)

