import logging
from datetime import datetime

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.routers import user
from app.routers.chat import chat_router
from app.routers.user import user_router
from fastapi.staticfiles import StaticFiles

# Initialize logger with basic configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Application metadata constants
APP_TITLE = "Shopping Assistant"
APP_VERSION = "1.0.0"

# Create the FastAPI application instance
app = FastAPI(title=APP_TITLE, version=APP_VERSION)

# Register routers
app.include_router(user.router)
app.include_router(user_router)
app.include_router(chat_router)

app.mount("/app/uploads", StaticFiles(directory="uploads"), name="uploads")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # TODO: For production, specify allowed origins explicitly.
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", tags=["Root"])
async def read_root():
    """
    Root endpoint for health check and confirmation of API setup.
    """
    return {
        "project": APP_TITLE,
        "status": "Active",
        "version": APP_VERSION,
        "documentation_url": "/docs",
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }
