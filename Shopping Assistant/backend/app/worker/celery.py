from app.core.config import settings
from celery import Celery

# Load configuration from settings
CELERY_BROKER_URL = settings.CELERY_BROKER_URL
CELERY_RESULT_BACKEND = settings.CELERY_RESULT_BACKEND

celery_app = Celery(
    "vexabot_tasks",
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND
)

# Update Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    broker_connection_retry_on_startup=True,
    worker_concurrency=4,
    worker_max_tasks_per_child=100,  # Prevent memory leaks
    result_expires=3600,  # Optionally expire results after 1 hour
)

# Auto-discover tasks in specified modules
celery_app.autodiscover_tasks(["app.worker.tasks"])
