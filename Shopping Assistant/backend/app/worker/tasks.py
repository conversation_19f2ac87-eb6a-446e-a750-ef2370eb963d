import logging

from celery import shared_task
from sqlalchemy.orm import sessionmaker

from app.database.database import sync_engine
from app.schemas.file_schema import ProcessFileData
from app.utils.file_utils import process_and_store_file_to_qdrant_sync

log = logging.getLogger(__name__)

sync_session_factory = sessionmaker(bind=sync_engine)

@shared_task(name="process_and_store_file_task")
def process_and_store_file_task(file_data_dict):
    """
    Celery task to process and store a file synchronously in the vector DB.
    """
    log.info(f"Starting process_and_store_file_task with data: {file_data_dict}")

    # Use a context manager for clean session handling
    with sync_session_factory() as db:
        try:
            # Convert the incoming dict to your data model
            file_data = ProcessFileData(**file_data_dict)

            # Execute synchronous logic (no async/await!)
            result = process_and_store_file_to_qdrant_sync(file_data, db)
            return result

        except Exception as exc:
            log.exception(f"Error while processing file: {exc}")
            db.rollback()  # ensure we roll back the transaction on error
            raise
