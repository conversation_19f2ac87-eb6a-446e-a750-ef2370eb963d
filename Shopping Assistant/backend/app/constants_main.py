from enum import StrEnum


class ErrorMessages(StrEnum):
    """Container for uniform error messages across the application."""

    DEFAULT = "Something went wrong :/"
    CREATE_USER_ERROR = (
        "Oops! Something went wrong while creating your account. "
        "Please try again later. If the issue persists, contact support for assistance."
    )
    UNAUTHORIZED = "401 Unauthorized"
    ACCESS_PROHIBITED = "You do not have permission to access this resource. Please contact your administrator for assistance."
    FILE_EXISTS = "Uh-oh! This file is already registered. Please choose another file."
    FORBIDDEN = "Forbidden: Access is restricted."
    CHAT_LIMIT_ERROR = "Oops, chat context limit exceeded. Please start a new chat."
    OPEN_AI_CHAT_LIMIT_ERROR = "context_length_exceeded"
    CHAT_ERROR = "Something went wrong while generating response, Try again."
    INVALID_CHAT_MODEL = "Invalid Chat Model."
    NOT_FOUND = "We could not find what you're looking for :/"
    FILE_NOT_PROCESSED = "Extracted content is not available for this file. Please ensure that the file is processed before proceeding."
    EMPTY_CONTENT = "The content provided is empty. Please ensure that there is text or data present before proceeding."


    @staticmethod
    def detailed_error(err: str) -> str:
        """Create detailed error message with error code."""
        return f"[ERROR: {err}]" if err else ErrorMessages.DEFAULT


TOO_MANY_REQUESTS_MESSAGE_START = "Too many"
TOO_FREQUENT_REQUESTS_MESSAGE_START = "Too frequent"
DEFAULT_PASSWORD_FOR_CREATED_USER = "shopping@123"
OPEN_AI_CHAT_MODAL = "gpt-4o-2024-08-06"

CHAT_FILE_ROLE = "user-files"
CHAT_USER_ROLE = "user"
CHAT_ASSISTANT_ROLE = "assistant"

#Model for embedding data and retrieval
NOMIC_EMBEDDING_MODEL = "nomic-embed-text:latest"
ALL_EMBEDDING_MODELS = ['nomic-embed-text:latest']

#AWS BEDROCK MODEL FORMATS
class ChatModelProviders:
    """Constants for model provider identifiers"""
    OPENAI = "openai"
    BEDROCK = "bedrock"
    OLLAMA = "ollama"

#AWS BEDROCK MODEL FORMATS
class AwsModelProviders:
    """Constants for model provider identifiers"""
    ANTHROPIC = "anthropic"
    AMAZON = "amazon"
    META = "meta"
    DEEPSEEK = "deepseek"
    MISTRAL = "mistral"

class ModelKeywords:
    """Keywords used to identify model providers"""
    ANTHROPIC = "anthropic"
    AMAZON = "amazon"
    BEDROCK = "bedrock"
    TITAN = "titan"
    META = "meta"
    LLAMA = "llama"
    DEEPSEEK = "deepseek"
    MISTRAL = "mistral"
