import logging
from datetime import datetime, timedelta, timezone
from typing import List, Optional
from uuid import UUID

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import delete, insert, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.constants import DEFAULT_PASSWORD_FOR_CREATED_USER
from app.models import User, User<PERSON><PERSON>, UserRole
from app.models.enums import AuthProviderType, RoleType, ResourceType
from app.schemas.user import (
    UserCreate, UserUpdate, UserLogin, LoginResponse, UserResponse, RoleResponse,
    ForgotPasswordRequest, ResetPasswordRequest, Token
)
from app.utils.auth_utils import SECRET_KEY, ALGORITHM, decode_token
from app.utils.auth_utils import create_access_token, create_refresh_token

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)


async def authenticate_user(db: AsyncSession, user_data: UserLogin) -> Optional[User]:
    """Authenticate user with email and password."""
    result = await db.execute(
        select(User).where(User.email == user_data.email)
    )
    user = result.scalars().first()
    
    if not user:
        return None
    
    # Get user auth for local provider
    auth_result = await db.execute(
        select(UserAuth).where(
            UserAuth.user_id == user.id,
            UserAuth.provider == AuthProviderType.LOCAL
        )
    )
    user_auth = auth_result.scalars().first()
    
    if not user_auth or not user_auth.hashed_password:
        return None
    
    if not verify_password(user_data.password, user_auth.hashed_password):
        return None
    
    return user


async def create_user(db: AsyncSession, user_data: UserCreate, current_user: User) -> User:
    """Create a new user with local authentication."""
    # Check if user already exists
    result = await db.execute(
        select(User).where(User.email == user_data.email)
    )
    if result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Create user
    user = User(
        email=user_data.email,
        username=user_data.username,
        is_active=True,
        is_super_admin=False
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # Create user auth
    user_auth = UserAuth(
        user_id=user.id,
        provider=AuthProviderType.LOCAL,
        hashed_password=get_password_hash(user_data.password)
    )
    db.add(user_auth)
    
    # Create default user role
    user_role = UserRole(
        user_id=user.id,
        role=RoleType.USER,
        resource_type=ResourceType.USER,
        permissions="read,write"
    )
    db.add(user_role)
    
    await db.commit()
    return user


async def signup_user(db: AsyncSession, user_data: UserCreate) -> User:
    """Handle user signup."""
    return await create_user(db, user_data, None)


# Login user and return token
async def login_user(db: AsyncSession, user_data: UserLogin) -> LoginResponse:
    user = await authenticate_user(db, user_data)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get user roles
    result = await db.execute(
        select(User)
        .options(selectinload(User.roles))
        .where(User.id == user.id)
    )
    user_with_roles = result.scalars().first()
    
    user_roles = []
    for role in user_with_roles.roles:
        user_roles.append(RoleResponse(
            role=role.role.value,
            resource_id=str(role.resource_id) if role.resource_id else None,
            resource_type=role.resource_type.value if role.resource_type else None
        ))

    # Create tokens
    access_token = create_access_token(data={"sub": str(user.id)})
    refresh_token = create_refresh_token(data={"sub": str(user.id)})

    return LoginResponse(
        token=Token(
            access_token=access_token,
            token_type="bearer",
            refresh_token=refresh_token
        ),
        user_data=UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            is_active=user.is_active,
            is_super_admin=user.is_super_admin,
            created_at=user.created_at,
            updated_at=user.updated_at
        ),
        user_roles=user_roles
    )


# Forgot password
async def forgot_password(db: AsyncSession, data: ForgotPasswordRequest) -> str:
    # Find the user by email
    result = await db.execute(select(User).where(User.email == data.email))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Generate reset token
    reset_token = create_access_token(
        data={"sub": str(user.id), "reset": True},
        expires_delta=timedelta(minutes=15)
    )
    
    return reset_token


# Reset password
async def reset_password(db: AsyncSession, data: ResetPasswordRequest) -> dict:
    try:
        # Decode token
        payload = decode_token(data.token)
        user_id = payload.get("sub")
        is_reset = payload.get("reset", False)
        
        if not user_id or not is_reset:
            raise HTTPException(status_code=400, detail="Invalid reset token")
        
        # Find user
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Update password
        auth_result = await db.execute(
            select(UserAuth).where(
                UserAuth.user_id == user.id,
                UserAuth.provider == AuthProviderType.LOCAL
            )
        )
        user_auth = auth_result.scalars().first()
        
        if not user_auth:
            # Create auth record if it doesn't exist
            user_auth = UserAuth(
                user_id=user.id,
                provider=AuthProviderType.LOCAL,
                hashed_password=get_password_hash(data.new_password)
            )
            db.add(user_auth)
        else:
            user_auth.hashed_password = get_password_hash(data.new_password)
        
        await db.commit()
        
        return {"message": "Password reset successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# Get user details
async def get_user_details(db: AsyncSession, user_id: UUID) -> UserResponse:
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return UserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        is_active=user.is_active,
        is_super_admin=user.is_super_admin,
        created_at=user.created_at,
        updated_at=user.updated_at
    )


# Update user
async def update_user(db: AsyncSession, user_id: UUID, user_data: UserUpdate, current_user_id: UUID) -> UserResponse:
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Update fields
    update_data = {}
    if user_data.email is not None:
        update_data["email"] = user_data.email
    if user_data.username is not None:
        update_data["username"] = user_data.username
    if user_data.is_active is not None:
        update_data["is_active"] = user_data.is_active
    
    if update_data:
        await db.execute(
            update(User).where(User.id == user_id).values(**update_data)
        )
        await db.commit()
        await db.refresh(user)
    
    # Update password if provided
    if user_data.password:
        auth_result = await db.execute(
            select(UserAuth).where(
                UserAuth.user_id == user.id,
                UserAuth.provider == AuthProviderType.LOCAL
            )
        )
        user_auth = auth_result.scalars().first()
        
        if user_auth:
            user_auth.hashed_password = get_password_hash(user_data.password)
        else:
            user_auth = UserAuth(
                user_id=user.id,
                provider=AuthProviderType.LOCAL,
                hashed_password=get_password_hash(user_data.password)
            )
            db.add(user_auth)
        
        await db.commit()
    
    return await get_user_details(db, user_id)


# Suspend user
async def suspend_user(db: AsyncSession, user_id: UUID, current_user_id: UUID) -> dict:
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.is_active = False
    await db.commit()
    
    return {"message": "User suspended successfully"}


# Reactivate user
async def reactivate_user(db: AsyncSession, user_id: UUID, current_user_id: UUID) -> dict:
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.is_active = True
    await db.commit()
    
    return {"message": "User reactivated successfully"}


# Delete user
async def delete_user(db: AsyncSession, user_id: UUID, current_user_id: UUID) -> dict:
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    await db.delete(user)
    await db.commit()
    
    return {"message": "User deleted successfully"}


# Search users
async def search_users(db: AsyncSession, query: str, skip: int = 0, limit: int = 100, requested_by: UUID = None) -> List[UserResponse]:
    # Build the search query
    search_query = select(User).where(
        User.email.contains(query) | User.username.contains(query)
    )
    
    # Add pagination
    search_query = search_query.offset(skip).limit(limit)
    
    # Execute the query
    result = await db.execute(search_query)
    users = result.scalars().all()
    
    # Convert to response format
    return [
        UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            is_active=user.is_active,
            is_super_admin=user.is_super_admin,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        for user in users
    ]


# List all users
async def list_all_users(db: AsyncSession) -> List[UserResponse]:
    result = await db.execute(select(User).order_by(User.created_at.desc()))
    users = result.scalars().all()
    
    return [
        UserResponse(
            id=user.id,
            email=user.email,
            username=user.username,
            is_active=user.is_active,
            is_super_admin=user.is_super_admin,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        for user in users
    ]
