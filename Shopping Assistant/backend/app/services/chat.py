import logging
import uuid
from datetime import datetime, timezone
from typing import List
from uuid import <PERSON><PERSON><PERSON>

from fastapi import H<PERSON>PException, status
from sqlalchemy import delete, insert
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.constants import ErrorMessages
from app.models import User
from app.models.chat import Chat, Message
from app.schemas.chat import (
    ChatRequest,
    ChatResponse,
    MessageResponse,
    UpdateTitleRequest
)
from app.services.langchain_service import LangChainService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChatService:
    def __init__(self):
        self.langchain_service = LangChainService()

    @staticmethod
    async def _validate_user_messages(messages):
        """Validate that there's at least one user message in the list."""
        if not any(msg.role == "user" for msg in messages):
            raise HTTPException(
                status_code=400, detail="At least one user message required"
            )

    @staticmethod
    async def _save_user_messages(db, chat_id, messages):
        """Save user messages to the database."""
        for message in messages:
            if message.role == "user":
                message_data = {
                    "chat_id": chat_id,
                    "role": "user",
                    "content": message.content,
                }
                await db.execute(insert(Message).values(message_data))
        await db.commit()

    @staticmethod
    async def _save_assistant_message(db, chat_id, content):
        """Save assistant message to the database."""
        message_data = {
            "chat_id": chat_id,
            "role": "assistant",
            "content": content,
        }
        await db.execute(insert(Message).values(message_data))
        await db.commit()

    async def chat(self, db: AsyncSession, request: ChatRequest, current_user: User):
        """Handle chat request with LangChain OpenAI response."""
        try:
            # Validate messages
            await ChatService._validate_user_messages(request.messages)

            # Get or create chat
            chat_id = request.chat_id or uuid.uuid4()
            if not request.chat_id:
                # Create new chat
                chat_data = {
                    "chat_id": chat_id,
                    "user_id": current_user.id,
                    "title": "New Chat"
                }
                await db.execute(insert(Chat).values(chat_data))
                await db.commit()

            # Save user messages
            await ChatService._save_user_messages(db, chat_id, request.messages)

            # Get chat history for context
            result = await db.execute(
                select(Message)
                .where(Message.chat_id == chat_id)
                .order_by(Message.timestamp)
            )
            chat_history = result.scalars().all()

            # Convert chat history to message format for LangChain
            messages_for_llm = [
                {"role": msg.role, "content": msg.content}
                for msg in chat_history
            ]

            # Get the latest user message
            latest_user_message = request.messages[-1].content if request.messages else ""

            # Get response from LangChain/OpenAI
            assistant_response = self.langchain_service.get_chat_response(
                user_message=latest_user_message,
                chat_history=messages_for_llm[:-1]  # Exclude the latest message as it's passed separately
            )

            # Save assistant response
            await ChatService._save_assistant_message(db, chat_id, assistant_response)

            return {
                "chat_id": str(chat_id),
                "response": assistant_response,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Error in chat: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ErrorMessages.CHAT_ERROR
            )

    @staticmethod
    async def update_chat_title(
        db: AsyncSession, chat_id: UUID, request: UpdateTitleRequest
    ) -> ChatResponse:
        """Update chat title."""
        try:
            result = await db.execute(
                select(Chat).where(Chat.chat_id == chat_id)
            )
            chat = result.scalars().first()
            
            if not chat:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Chat not found"
                )

            chat.title = request.title
            chat.updated_at = datetime.now(timezone.utc)
            await db.commit()

            return ChatResponse(
                chat_id=str(chat.chat_id),
                title=chat.title,
                created_at=chat.created_at,
                updated_at=chat.updated_at
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating chat title: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update chat title"
            )

    @staticmethod
    async def get_chat_history(
            db: AsyncSession, chat_id: UUID
    ) -> list[MessageResponse]:
        """Get chat history."""
        try:
            result = await db.execute(
                select(Message)
                .where(Message.chat_id == chat_id)
                .order_by(Message.timestamp)
            )
            messages = result.scalars().all()

            return [
                MessageResponse(
                    message_id=str(msg.message_id),
                    role=msg.role,
                    content=msg.content,
                    timestamp=msg.timestamp
                )
                for msg in messages
            ]

        except Exception as e:
            logger.error(f"Error getting chat history: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get chat history"
            )

    @staticmethod
    async def get_user_chats(db: AsyncSession, user_id: UUID) -> list[ChatResponse]:
        """Get all chats for a user."""
        try:
            result = await db.execute(
                select(Chat)
                .where(Chat.user_id == user_id)
                .order_by(Chat.updated_at.desc())
            )
            chats = result.scalars().all()

            return [
                ChatResponse(
                    chat_id=str(chat.chat_id),
                    title=chat.title,
                    created_at=chat.created_at,
                    updated_at=chat.updated_at
                )
                for chat in chats
            ]

        except Exception as e:
            logger.error(f"Error getting user chats: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get user chats"
            )

    @staticmethod
    async def get_chat_details(db: AsyncSession, chat_id: UUID) -> ChatResponse:
        """Get chat details."""
        try:
            result = await db.execute(
                select(Chat).where(Chat.chat_id == chat_id)
            )
            chat = result.scalars().first()

            if not chat:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Chat not found"
                )

            return ChatResponse(
                chat_id=str(chat.chat_id),
                title=chat.title,
                created_at=chat.created_at,
                updated_at=chat.updated_at
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chat details: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get chat details"
            )

    @staticmethod
    async def delete_chat(db: AsyncSession, chat_id: UUID) -> dict:
        """Delete a chat and all its messages."""
        try:
            # Delete messages first (due to foreign key constraint)
            await db.execute(
                delete(Message).where(Message.chat_id == chat_id)
            )
            
            # Delete chat
            await db.execute(
                delete(Chat).where(Chat.chat_id == chat_id)
            )
            
            await db.commit()

            return {"message": "Chat deleted successfully"}

        except Exception as e:
            logger.error(f"Error deleting chat: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete chat"
            )
