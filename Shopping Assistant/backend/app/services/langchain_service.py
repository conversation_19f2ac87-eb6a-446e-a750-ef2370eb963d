import json
import os
import logging
from typing import List
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from app.core.config import settings
from app.constants.system_prompts import SHOPPING_ASSISTANT_SYSTEM_PROMPT


logger = logging.getLogger(__name__)


class LangChainService:
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0.7
        )
        self.products_data = self._load_products()
        self.system_prompt = self._create_system_prompt()

    def _load_products(self):
        """Load products from the JSON file."""
        try:
            # Get the directory where this file is located
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # Navigate to the constants directory and load products.json
            products_file = os.path.join(current_dir, '..', 'constants', 'products.json')
            
            with open(products_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {"products": []}
        except json.JSONDecodeError:
            return {"products": []}

    def _create_system_prompt(self):
        """Create system prompt with full product JSON data included."""
        products_json = json.dumps(self.products_data, indent=2)
        return f"{SHOPPING_ASSISTANT_SYSTEM_PROMPT}\n\n**PRODUCT CATALOG DATA (JSON):**\n```json\n{products_json}\n```"

    def get_chat_response(self, user_message: str, chat_history: list = None) -> str:
        """
        Get a response from the AI model based on user message and chat history.
        
        Args:
            user_message: The current user message
            chat_history: List of previous messages in the conversation
            
        Returns:
            AI response as a string
        """
        messages = [SystemMessage(content=self.system_prompt)]
        
        # Add chat history if provided
        if chat_history:
            for msg in chat_history:
                if msg.get('role') == 'user':
                    messages.append(HumanMessage(content=msg.get('content', '')))
                elif msg.get('role') == 'assistant':
                    messages.append(SystemMessage(content=msg.get('content', '')))
        
        # Add current user message
        messages.append(HumanMessage(content=user_message))
        
        try:
            response = self.llm.invoke(messages)
            return response.content
        except Exception as e:
            return f"I apologize, but I encountered an error: {str(e)}. Please try again." 