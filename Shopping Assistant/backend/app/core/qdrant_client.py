from fastapi import HTTPException, status
from qdrant_client import Qdrant<PERSON>lient

from app.core.config import settings


def get_qdrant_client() -> QdrantClient:
    """
    Create and return a QdrantClient instance configured with application settings.

    This function initializes the Qdrant client using the host and port specified
    in the settings. It can be extended to include additional parameters (e.g., API keys,
    SSL configuration) as needed.

    Returns:
        QdrantClient: A client instance for interacting with the Qdrant vector database.

    Raises:
        Exception: If client initialization fails.
    """
    try:
        client = QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT,timeout=600 )
        return client
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to connect to the Qdrant service. Please check the server settings.", )


# Initialize the global Qdrant client instance.
client = get_qdrant_client()
