from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from uuid import UUID

from app.models.user import User


async def get_user_by_id(db: AsyncSession, user_id: UUID) -> User:
    """Get user by ID."""
    result = await db.execute(
        select(User).where(User.id == user_id)
    )
    return result.scalars().first()


def has_permission(user: User, resource_type: str, resource_id: str = None, permission: str = "read") -> bool:
    """Simple permission check - for now, all authenticated users have basic permissions."""
    return user.is_active 