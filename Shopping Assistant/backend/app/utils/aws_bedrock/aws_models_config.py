AWS_BEDROCK_MODELS = [
    # {"name": "Nova Pro", "tag": "apac.amazon.nova-pro-v1:0", "provider": "bedrock", "format": "amazon", "deployment": "cloud"},
    # {"name": "Nova Lite", "tag": "apac.amazon.nova-lite-v1:0", "provider": "bedrock", "format": "amazon", "deployment": "cloud"},
    # {"name": "Nova Micro", "tag": "apac.amazon.nova-micro-v1:0", "provider": "bedrock", "format": "amazon", "deployment": "cloud"},
    {"name": "Claude 3 Sonnet", "tag": "apac.anthropic.claude-3-sonnet-20240229-v1:0", "provider": "bedrock", "format": "anthropic", "deployment": "cloud"},
    {"name": "Claude 3 Haiku", "tag": "anthropic.claude-3-haiku-20240307-v1:0", "provider": "bedrock", "format": "anthropic", "deployment": "cloud"},
    {"name": "Claude v2", "tag": "anthropic.claude-v2:0", "provider": "bedrock", "format": "anthropic", "deployment": "cloud"},
    {"name": "Claude Instant", "tag": "anthropic.claude-instant-v1", "provider": "bedrock", "format": "anthropic", "deployment": "cloud"},
]
