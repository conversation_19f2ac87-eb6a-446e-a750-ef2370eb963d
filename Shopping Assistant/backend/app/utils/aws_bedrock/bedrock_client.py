import boto3
import botocore

from app.core.config import settings

# Configure AWS Bedrock client
bedrock_client = boto3.client(
    "bedrock-runtime",
    region_name=settings.BEDROCK_AWS_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY,
    aws_secret_access_key=settings.AWS_SECRET_KEY,
    config=botocore.config.Config(
        retries={'max_attempts': 2},
        connect_timeout=5,
        read_timeout=30
    )
)
