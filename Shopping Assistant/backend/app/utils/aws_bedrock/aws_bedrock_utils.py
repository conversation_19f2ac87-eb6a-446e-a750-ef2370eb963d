import json
import re

from app.constants import ModelKeywords, AwsModelProviders
from app.schemas.chat import IsGreeting, RefinedQuery
from app.utils.chat_configs import RAG_LLM_TEMPERATURE, LLM_TOP_P, LLM_MAX_TOKENS
from app.utils.vexabot_system_prompt import SYSTEM_PROMPT


def extract_assistant_content(response_body: dict, model_format: str) -> str:
    """
    Extracts the assistant's response content from the API response based on the model format.
    """
    try:
        if model_format == AwsModelProviders.ANTHROPIC:
            return response_body.get('content', [{}])[0].get('text', '')
        elif model_format == AwsModelProviders.AMAZON:
            return response_body.get('results', [{}])[0].get('outputText', '')
        elif model_format == AwsModelProviders.META:
            return clean_llama_output(response_body.get('generation', ''))
        elif model_format == AwsModelProviders.DEEPSEEK:
            return response_body.get('choices', [{}])[0].get('message', {}).get('content', '')
        elif model_format == AwsModelProviders.MISTRAL:
            return response_body.get('outputs', [{}])[0].get('text', '')
        else:
            return ''
    except (KeyError, IndexError, AttributeError):
        return ''


def determine_model_format(model: str) -> str:
    """
    Determines the model format based on the model ID string.
    """
    model_lower = model.lower()

    if ModelKeywords.ANTHROPIC in model_lower:
        return AwsModelProviders.ANTHROPIC
    elif (ModelKeywords.AMAZON in model_lower or
          ModelKeywords.BEDROCK in model_lower or
          ModelKeywords.TITAN in model_lower):
        return AwsModelProviders.AMAZON
    elif (ModelKeywords.META in model_lower or
          ModelKeywords.LLAMA in model_lower):
        return AwsModelProviders.META
    elif ModelKeywords.DEEPSEEK in model_lower:
        return AwsModelProviders.DEEPSEEK
    elif ModelKeywords.MISTRAL in model_lower:
        return AwsModelProviders.MISTRAL

    return AwsModelProviders.ANTHROPIC


def clean_llama_output(text):
    """Clean Llama model output by removing special tokens."""
    return (
        text.replace("<s>", "")
        .replace("</s>", "")
        .replace("[INST]", "")
        .replace("[/INST]", "")
        .replace("<|eot_id|>", "")
        .strip()
    )

###################################################################################
### AWS Bedrock Input Prompt Formating
###################################################################################


def format_llama_prompt(conversation, prompt):
    formatted_messages = ""
    # Add system prompt first
    formatted_messages += f"<|start_header_id|>system<|end_header_id|>\n{ prompt if prompt else SYSTEM_PROMPT}\n<|eot_id|>\n"

    for msg in conversation:
        role = "user" if msg["role"] == "user" else "assistant"
        formatted_messages += f"<|start_header_id|>{role}<|end_header_id|>\n{msg['content']}\n<|eot_id|>\n"

    # Add the final assistant header
    formatted_messages += "<|start_header_id|>assistant<|end_header_id|>\n"
    return "<|begin_of_text|>" + formatted_messages


def format_mistral_prompt(conversation, prompt):
    # Add system prompt at the beginning
    prompt = f"<s>[INST] {prompt if prompt else SYSTEM_PROMPT} [/INST]\n\n"

    for i, msg in enumerate(conversation):
        if msg["role"] == "user":
            prompt += f"[INST] {msg['content'].strip()} [/INST]"
        else:
            prompt += f"{msg['content'].strip()} "

    # If the last message was from assistant, we need to add a user instruction to continue
    if conversation and conversation[-1]["role"] == "assistant":
        prompt += "[INST] Please continue [/INST]"
    return prompt

###################################################################################
### AWS Bedrock Payload preparation
###################################################################################

def fetch_bedrock_chat_payload(
        model_format: str,
        conversation: list,
        prompt: str = None,
        system_prompt: str = SYSTEM_PROMPT,
        temperature: float = RAG_LLM_TEMPERATURE,
        max_tokens: int = LLM_MAX_TOKENS,
        top_p: float = LLM_TOP_P,
        model: str = None,
) -> dict:
    """
    Prepares the API payload based on the model format and parameters.

    Args:
        model_format (str): Model format ("anthropic", "amazon", "meta", "deepseek", "mistral")
        conversation (list): List of message dictionaries with role/content
        prompt (str, optional): The user prompt. Defaults to None.
        system_prompt (str, optional): System prompt. Defaults to SYSTEM_PROMPT.
        temperature (float, optional): Sampling temperature. Defaults to RAG_LLM_TEMPERATURE.
        max_tokens (int, optional): Maximum tokens to generate. Defaults to 2000.
        top_p (float, optional): Nucleus sampling parameter. Defaults to 0.7.
        model (str, optional): Model identifier (needed for some format variations). Defaults to None.

    Returns:
        dict: The prepared payload for the API call
    """
    final_prompt = prompt if prompt else system_prompt

    if model_format == AwsModelProviders.ANTHROPIC:
        payload = {
            "anthropic_version": "bedrock-2023-05-31",
            "messages": conversation,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "system": final_prompt
        }

    elif model_format == AwsModelProviders.AMAZON:
        # For Amazon models, concatenate history with clear role markers
        conversation_text = f"System: {final_prompt}\n\n"
        for msg in conversation:
            role_prefix = "User: " if msg["role"] == "user" else "Assistant: "
            conversation_text += f"{role_prefix}{msg['content']}\n\n"

        payload = {
            "inputText": conversation_text.strip(),
            "textGenerationConfig": {
                "maxTokenCount": max_tokens,
                "temperature": temperature,
                "topP": top_p,
                "stopSequences": []
            }
        }

    elif model_format == AwsModelProviders.META:
        # Check if it's Llama 4 or Llama 3
        formatted_prompt = format_llama_prompt(conversation, final_prompt)
        payload = {
            "prompt": formatted_prompt,
            "max_gen_len": max_tokens,
            "temperature": temperature,
            "top_p": top_p
        }

    elif model_format == AwsModelProviders.DEEPSEEK:

        # Create a copy to avoid modifying the original
        deepseek_conversation = conversation.copy()
        deepseek_conversation.insert(0, {"role": "system", "content": final_prompt})

        payload = {
            "messages": deepseek_conversation,
            "temperature": temperature,
            "top_p": top_p,
            "max_tokens": max_tokens
        }

    elif model_format == AwsModelProviders.MISTRAL:
        formatted_prompt = format_mistral_prompt(conversation, final_prompt)
        payload = {
            "prompt": formatted_prompt,
            "temperature": temperature,
            "top_p": top_p,
            "max_tokens": max_tokens
        }

    else:
        raise ValueError(f"Unsupported model format: {model_format}")

    return payload


def get_structured_bedrock_response(
    bedrock_client, model_id, model_format, prompt: str,response_schema:dict,response_model, max_tokens: int = 50
) :
    try:

        # Build Bedrock payload
        if model_format == AwsModelProviders.ANTHROPIC:
            payload = {
                "anthropic_version": "bedrock-2023-05-31",
                "system": f"You must respond with valid JSON only, following this schema: {json.dumps(response_schema)}",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens
            }
        else:
            payload = {
                "schemaVersion": "messages-v1",
                "messages": [{"role": "user", "content": [{"text": prompt}]}],
                "inferenceConfig": {
                    "max_new_tokens": max_tokens,
                    "temperature": 0.0
                },
                "system": [{"text": f"You must respond with valid JSON only, following this schema: {json.dumps(response_schema)}"}]
            }
        # Invoke model
        response = bedrock_client.invoke_model(
            modelId=model_id,
            body=json.dumps(payload),
            contentType="application/json",
            accept="application/json"
        )
        # Parse model response
        result = json.loads(response["body"].read().decode("utf-8"))
        # Extract text content
        if model_format == AwsModelProviders.ANTHROPIC:
            response_text = result.get("content", [{}])[0].get("text", "")
        else:
            if "output" in result and isinstance(result["output"], dict):
                response_text = result["output"].get("message", {}).get("content", [{}])[0].get("text", "")
            elif "output" in result and isinstance(result["output"], list):
                response_text = result["output"][0].get("text", "")
            elif "content" in result:
                response_text = result["content"][0].get("text", "")
            else:
                return None
        # Try parsing JSON strictly
        try:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                raw_json = response_text[json_start:json_end]
                parsed = json.loads(raw_json)
                return response_model(**parsed).dict()
        except (json.JSONDecodeError, ValueError):
            match = re.search(r"```(?:json)?\s*([\s\S]+?)\s*```", response_text)
            if match:
                try:
                    return response_model(**json.loads(match.group(1))).dict()
                except Exception:
                    return None
        return None
    except Exception as e:
        print(f"Error getting structured greeting response: {str(e)}")
        return None