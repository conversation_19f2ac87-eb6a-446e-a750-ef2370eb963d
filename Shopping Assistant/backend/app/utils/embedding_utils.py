import logging
import requests
from typing import Callable, List

from app.core.config import settings

# Configure logging
log = logging.getLogger(__name__)

# Ollama and Qdrant configuration
OLLAMA_URL = settings.OLLAMA_URL
EMBEDDING_URL = f"{OLLAMA_URL}/api/embeddings"


def get_embedding_function(provider: str, model_name: str) -> Callable[[str], List[float]]:
    """
    Returns an embedding function for the specified provider and model.

    This function currently supports the "ollama" provider. You can extend it
    to include other providers in the future.

    Args:
        provider (str): The embedding provider. Currently only "ollama" is supported.
        model_name (str): The model to be used for generating embeddings.

    Returns:
        Callable[[str], List[float]]: A function that accepts a text string and
        returns a list of floats representing the embedding vector.

    Raises:
        ValueError: If an unsupported provider is specified.
    """

    if provider.lower() == settings.OLLAMA_HOST:
        def ollama_embedding_function(input_text: str) -> List[float]:
            """
            Generate an embedding vector for the given text using Ollama's API.

            Args:
                input_text (str): The text for which you want to obtain the embedding.

            Returns:
                List[float]: The embedding vector for the input text.
            """
            try:
                # Replace newlines and strip leading/trailing spaces for cleaner input
                processed_text = input_text.replace("\n", " ").strip()

                # Send a POST request to Ollama's embedding endpoint
                response = requests.post(
                    EMBEDDING_URL,
                    json={"model": model_name, "prompt": processed_text},
                )

                # Raise an HTTPError if the request returned an unsuccessful status
                response.raise_for_status()

                # Parse the response to extract the embedding vector
                data = response.json()
                return data.get("embedding", [])

            except requests.exceptions.RequestException as request_err:
                log.error(f"Request error when fetching embedding: {request_err}")
                return []
            except Exception as e:
                log.exception(f"An unexpected error occurred: {e}")
                return []

        return ollama_embedding_function

    # If the provider is not supported, raise a ValueError
    raise ValueError(f"Unsupported provider: {provider}")
