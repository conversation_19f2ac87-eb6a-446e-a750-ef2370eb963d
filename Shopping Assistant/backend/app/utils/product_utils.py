"""
Utility functions for product management in the Shopping Assistant.
"""

import json
import os
from typing import List, Dict, Optional


def load_products() -> Dict:
    """
    Load products from the JSON file.
    
    Returns:
        Dictionary containing product data
    """
    try:
        # Get the directory where this file is located
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Navigate to the constants directory and load products.json
        products_file = os.path.join(current_dir, '..', 'constants', 'products.json')
        
        with open(products_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {"products": []}
    except json.JSONDecodeError:
        return {"products": []}


def get_product_by_id(product_id: str) -> Optional[Dict]:
    """
    Get a specific product by its ID.
    
    Args:
        product_id: The product ID to search for
        
    Returns:
        Product dictionary if found, None otherwise
    """
    products_data = load_products()
    for product in products_data.get('products', []):
        if product.get('id') == product_id:
            return product
    return None


def get_products_by_category(category: str) -> List[Dict]:
    """
    Get all products in a specific category.
    
    Args:
        category: The category to filter by
        
    Returns:
        List of products in the specified category
    """
    products_data = load_products()
    return [
        product for product in products_data.get('products', [])
        if product.get('category', '').lower() == category.lower()
    ]


def get_products_by_price_range(min_price: float, max_price: float) -> List[Dict]:
    """
    Get products within a specific price range.
    
    Args:
        min_price: Minimum price
        max_price: Maximum price
        
    Returns:
        List of products within the price range
    """
    products_data = load_products()
    return [
        product for product in products_data.get('products', [])
        if min_price <= product.get('price', 0) <= max_price
    ]


def search_products(query: str) -> List[Dict]:
    """
    Search products by name, brand, or description.
    
    Args:
        query: Search query string
        
    Returns:
        List of products matching the search query
    """
    products_data = load_products()
    query_lower = query.lower()
    
    matching_products = []
    for product in products_data.get('products', []):
        # Search in name, brand, and description
        if (query_lower in product.get('name', '').lower() or
            query_lower in product.get('brand', '').lower() or
            query_lower in product.get('description', '').lower()):
            matching_products.append(product)
    
    return matching_products


def get_all_products() -> List[Dict]:
    """
    Get all available products.
    
    Returns:
        List of all products
    """
    products_data = load_products()
    return products_data.get('products', [])


def get_products_summary() -> str:
    """
    Get a formatted summary of all products for the system prompt.
    
    Returns:
        Formatted string with product information
    """
    products = get_all_products()
    summary = "**Available Products:**\n\n"
    
    for i, product in enumerate(products, 1):
        summary += f"{i}. **{product['name']}** ({product['brand']}) - ${product['price']}\n"
        summary += f"   - {product['category']} category\n"
        summary += f"   - Features: {', '.join(product['features'])}\n"
        summary += f"   - Rating: {product['rating']}/5\n\n"
    
    return summary 