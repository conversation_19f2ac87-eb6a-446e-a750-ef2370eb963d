import asyncio
import json
import logging

from fastapi import HTT<PERSON><PERSON>x<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.constants import ChatModelProviders
from app.constants import NOMIC_EMBEDDING_MODEL
from app.core.config import settings
from app.database.vector_dependency import get_vector_service
from app.models.chat import Message
from app.schemas.chat import RefinedQuery
from app.services.kb_service import get_knowledge_base
from app.utils.aws_bedrock.aws_bedrock_utils import determine_model_format, get_structured_bedrock_response
from app.utils.aws_bedrock.bedrock_client import bedrock_client
from app.utils.chat_configs import OLLAMA_GENERATE_API_URL
from app.utils.chat_configs import QDRANT_QUERY_RESULT_COUNT, QDRANT_QUERY_SCORE_THRESHOLD, \
    RETRIEVED_TOP_RESULT_COUNT
from app.utils.embedding_utils import get_embedding_function
from app.utils.vexabot_system_prompt import query_enhancer_prompt
from .chat_utils import get_provider_for_chat_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Assuming you already have this from your code
vector_service = get_vector_service()

# Get the embedding function and embed all document texts.
embedding_function = get_embedding_function(
    settings.OLLAMA_HOST,
    NOMIC_EMBEDDING_MODEL
)


async def _get_enhanced_search_query(db: AsyncSession, chat_id: str, search_query: str, request) -> str:
    """Enhance search query using chat history context."""
    return await generate_meaningful_query(db, chat_id, search_query, request)


def _generate_query_embedding(search_query: str) -> list:
    """Generate embedding vector for search query."""
    embedding = embedding_function(search_query)
    if not embedding:
        raise HTTPException(status_code=500, detail="Failed to generate query embedding")
    return embedding


def _format_retrieved_chunks(chunks: list) -> str:
    """Format retrieved chunks into readable string."""
    formatted_chunks = [
        f"[Source: {chunk['file_name'] or chunk['sub_url'] }]\n"
        f"{chunk['text']}\n"
        "-----------------------------------------------------"
        for chunk in chunks
    ]

    return "\n".join(formatted_chunks) if formatted_chunks else ""


async def _search_knowledge_base(db: AsyncSession, kb_id: str, query_embedding: list) -> tuple:
    """Search a single knowledge base and return results with KB name."""
    kb = await get_knowledge_base(db, kb_id)
    if not kb:
        return None

    collection_name = f"vexabot_kb_{kb_id}"
    try:
        search_results = vector_service.query_points(
            collection_name=collection_name,
            query_embedding=query_embedding,
            limit=QDRANT_QUERY_RESULT_COUNT,
            score_threshold=QDRANT_QUERY_SCORE_THRESHOLD
        )

        results = [{
            "text": result.payload.get("text", ""),
            "file_name": result.payload.get("metadata", {}).get("filename", None),
            "source_url": result.payload.get("metadata", {}).get("source_url", None),
            "sub_url": result.payload.get("metadata", {}).get("sub_url", None),
            "score": result.score,
            "kb_name": kb.name
        } for result in search_results]

        return results, kb.name

    except Exception as e:
        logger.error(f"Error searching knowledge base {kb_id}: {str(e)}")
        return [], kb.name


async def perform_rag_and_retrieve_data(db: AsyncSession, kb_ids: list, search_query: str, chat_id: str ,request) -> str:
    """
    Perform RAG (Retrieval-Augmented Generation) by retrieving relevant content from knowledge bases.
    Returns a list of retrieved content chunks.
    """
    # Enhanced query generation with chat history context
    search_query = await _get_enhanced_search_query(db, chat_id, search_query, request)

    query_embedding = _generate_query_embedding(search_query)

    # Process all knowledge bases concurrently
    kb_tasks = [_search_knowledge_base(db, kb_id, query_embedding) for kb_id in kb_ids]
    kb_results_raw = await asyncio.gather(*kb_tasks)

    # Filter and process results
    all_kb_contents = []
    kb_names = []

    for result in kb_results_raw:
        if result is not None:
            contents, name = result
            if contents:
                all_kb_contents.extend(contents)
                kb_names.append(name)

    # Sort and select top results
    all_kb_contents.sort(key=lambda x: x["score"], reverse=True)
    top_results = all_kb_contents[:RETRIEVED_TOP_RESULT_COUNT]

    # Extract references with both file_name and sub_url when available
    references = []
    for result in top_results:
        # Handle file references
        if result.get('file_name'):
            references.append({
                'name': result['file_name'],
                'url': '#'  # Fallback to '#' #TODO : Add file path url
            })

        # Handle sub_url references
        if result.get('sub_url') :
            references.append({
                'name': result['sub_url'],
                'url': result['sub_url']
            })

    retrieved_data = _format_retrieved_chunks(top_results)

    logger.info(f"Retrieved Chunks: {retrieved_data}")

    return retrieved_data, references


async def _enhance_query_with_llm(prompt: str, search_query: str, request) -> str:
    """
    Helper function to send query enhancement request to LLM and parse response
    Args:
        prompt: Complete prompt to send to LLM
        search_query: Original query as fallback
    Returns:
        Enhanced query or original if enhancement fails
    """

    provider = get_provider_for_chat_model(request.chat_model)
    if provider == ChatModelProviders.OLLAMA:
        payload = {
            "model": request.chat_model,
            "prompt": prompt,
            "stream": False,
            "format": RefinedQuery.model_json_schema()
        }

        try:
            import requests
            response = requests.post(OLLAMA_GENERATE_API_URL, json=payload)
            response_data = response.json()

            # Extract and parse the JSON from the response
            refined_query_json = json.loads(response_data["response"])
            refined_query = refined_query_json.get("refined_query", search_query)
            logger.info(f"Refined Query from Ollama: {refined_query}")
            return refined_query

        except Exception as e:
            logger.error(f"Error generating meaningful query: {e}")
            return search_query  # Fallback to original query if something fails
    elif provider == ChatModelProviders.BEDROCK:
        try:
            # Determine the model format based on the model name
            model_format = determine_model_format(request.chat_model)
            response_schema = {"type": "object", "properties": {"refined_query": {"type": "string"}}}

            structured_result = get_structured_bedrock_response(
                bedrock_client=bedrock_client,
                model_id=request.chat_model,
                model_format=model_format,
                prompt=prompt,
                response_schema=response_schema,
                response_model=RefinedQuery
            )

            if structured_result:
                refined_query = structured_result.get("refined_query", search_query)
                logger.info(f"Refined Query from Bedrock: {refined_query}")
                return refined_query
            return search_query
        except Exception as e:
            logger.error(f"Error parsing Ollama response: {e}")
            return search_query
    return search_query


async def generate_meaningful_query(db: AsyncSession, chat_id: str, search_query: str, request):
    """
    Generates a meaningful search query for vector DB search based on the user's query and chat history.
    """
    # Get recent chat history
    chat_history = await get_recent_chat_history(db, chat_id)

    # If chat history is empty return the original query
    if not chat_history:
        return search_query

    # Extract user and assistant messages from the chat history
    context = "\n".join(
        [
            f"User: {exchange.content}\nAssistant: {next_exchange.content}"
            for exchange, next_exchange in zip(chat_history[::2], chat_history[1::2])
            if exchange.role == "user" and next_exchange.role == "assistant"
        ]
    )

    prompt = query_enhancer_prompt(search_query, context)
    return await _enhance_query_with_llm(prompt, search_query, request)


async def get_recent_chat_history(
        db: AsyncSession,
        chat_id: str
) -> list[Message]:
    """
    Fetch recent chat messages for a given chat_id.
    """
    from app.models import Message
    result = await db.execute(
        select(Message)
        .where(Message.chat_id == chat_id)
        .order_by(Message.timestamp)
        .limit(5)
    )

    messages = result.scalars().all()

    return messages