"""
custom_exception.py

This module defines custom exception classes to be used throughout an application.
Each exception class is tailored to represent a specific error scenario.
"""

class DatabaseUpdateError(Exception):
    """
    Exception raised when a database update operation fails.

    Attributes:
        message (str): Explanation of the error.
    """

    def __init__(self, message="An error occurred while updating the database."):
        super().__init__(message)
        self.message = message


class FileNotFoundError(Exception):
    """
    Exception raised when a required file is not found.

    Attributes:
        message (str): Explanation of the error.
    """

    def __init__(self, message="The specified file was not found."):
        super().__init__(message)
        self.message = message


class DataMergeError(Exception):
    """
    Exception raised when data merging fails.

    Attributes:
        message (str): Explanation of the error.
    """

    def __init__(self, message="Data merge operation failed."):
        super().__init__(message)
        self.message = message


class MetadataMergeError(Exception):
    """Exception raised when metadata merging fails"""

