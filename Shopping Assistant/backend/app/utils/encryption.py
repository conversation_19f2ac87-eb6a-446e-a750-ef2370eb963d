import os
from cryptography.fernet import Fernet

from app.core.config import settings

# Typically you'd load from environment or secrets manager
ENCRYPTION_KEY = settings.ENCRYPTION_KEY

if not ENCRYPTION_KEY:
    raise ValueError("No ENCRYPTION_KEY set in environment variables")

fernet = Fernet(ENCRYPTION_KEY.encode() if isinstance(ENCRYPTION_KEY, str) else ENCRYPTION_KEY)

def encrypt_secret(secret_str: str) -> str:
    """
    Encrypts a secret string using the Fernet key, returning a base64-encoded cipher.
    """
    encrypted = fernet.encrypt(secret_str.encode("utf-8"))
    return encrypted.decode("utf-8")

def decrypt_secret(encrypted_str: str) -> str:
    """
    Decrypts a base64-encoded cipher text using the Fernet key, returning the original string.
    """
    decrypted = fernet.decrypt(encrypted_str.encode("utf-8"))
    return decrypted.decode("utf-8")
