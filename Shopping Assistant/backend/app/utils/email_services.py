import logging

# Initialize logger with basic configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def send_otp_email(email: str, otp: str):
    """Placeholder function for sending OTP via email."""
    logger.info(f"OTP {otp} would be sent to {email} (email functionality disabled)")


async def send_reset_email(email_to: str, reset_token: str):
    """Placeholder function for sending reset email."""
    logger.info(f"Reset token {reset_token} would be sent to {email_to} (email functionality disabled)")


async def send_user_invite_email(email: str, sender_email: str, token, project_name=None, organization_name=None):
    """Placeholder function for sending invitation email."""
    logger.info(f"Invitation token {token} would be sent to {email} (email functionality disabled)")
