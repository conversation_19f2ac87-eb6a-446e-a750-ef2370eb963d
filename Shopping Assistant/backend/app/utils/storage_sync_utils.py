import os
import shutil
import logging
from abc import ABC, abstractmethod
from typing import <PERSON>ary<PERSON>, <PERSON><PERSON>, List, Optional
from io import BytesIO

from app.core.config import settings

# Configure logging
log = logging.getLogger(__name__)

# Ensure upload directory exists
UPLOAD_DIR = "/app/uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)


class StorageProvider(ABC):
    """
    Abstract base class for storage providers.
    """

    @abstractmethod
    def get_file(self, file_path: str) -> str:
        pass

    @abstractmethod
    def upload_file(self, file: BinaryIO, filename: Optional[str] = None, target_path: Optional[str] = None) -> Tuple[
        bytes, str]:
        pass

    @abstractmethod
    def delete_file(self, file_path: str) -> None:
        pass

    @abstractmethod
    def delete_all_files(self) -> None:
        pass


class LocalStorageProvider(StorageProvider):
    """
    Local file storage provider.
    """

    @staticmethod
    def upload_file(file: BinaryIO, filename: Optional[str] = None, target_path: Optional[str] = None) -> Tuple[
        bytes, str]:
        contents = file.read()
        if not contents:
            raise ValueError("File content is empty")

        file_path = target_path or os.path.join(UPLOAD_DIR, filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        with open(file_path, "wb") as f:
            f.write(contents)

        log.info(f"File uploaded successfully to {file_path}")
        return contents, file_path

    @staticmethod
    def get_file(file_path: str) -> str:
        return file_path

    @staticmethod
    def delete_file(file_path: str) -> None:
        if os.path.isfile(file_path):
            os.remove(file_path)
            log.info(f"File {file_path} deleted successfully.")
        else:
            log.warning(f"File {file_path} not found in local storage.")

    @staticmethod
    def delete_all_files() -> None:
        if os.path.exists(UPLOAD_DIR):
            for filename in os.listdir(UPLOAD_DIR):
                file_path = os.path.join(UPLOAD_DIR, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                    log.info(f"Deleted: {file_path}")
                except Exception as err:
                    log.error(f"Failed to delete {file_path}. Reason: {err}")
        else:
            log.warning(f"Directory '{UPLOAD_DIR}' not found.")


def get_storage_provider(storage_provider: str) -> StorageProvider:
    """
    Factory function to return the appropriate storage provider.
    """
    if storage_provider == "local":
        return LocalStorageProvider()
    else:
        raise RuntimeError(f"Unsupported storage provider: {storage_provider}")


# Initialize the storage provider
Storage: StorageProvider = get_storage_provider("local")
