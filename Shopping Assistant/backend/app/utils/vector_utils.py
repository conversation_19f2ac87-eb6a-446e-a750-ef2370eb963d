import json
import logging
import uuid
from typing import Optional, List

from fastapi import Request
from langchain.text_splitter import TokenTextSplitter
from langchain_core.documents import Document

from app.constants import ErrorMessages, NOMIC_EMBEDDING_MODEL
from app.core.config import settings
from app.core.qdrant_client import client
from app.database.vector_dependency import get_vector_service
from app.utils.embedding_utils import get_embedding_function

vector_service = get_vector_service()

log = logging.getLogger(__name__)
log.setLevel("INFO")

OLLAMA_URL = settings.OLLAMA_URL
HOST_NAME = settings.OLLAMA_HOST

log = logging.getLogger(__name__)

def save_docs_to_vector_db(
    request: Request,
    docs: List[Document],
    collection_name: str,
    metadata: Optional[dict] = None,
    overwrite: bool = False,
    split: bool = True,
    add: bool = False,
) -> bool:
    """
    Saves a list of documents to a vector database collection.
    Keeping this as an old file for referring. We need some of these when we progres to other files
    This function can optionally split the documents into smaller chunks based on token count,
    embed them using the configured embedding service, and store them in the given collection.
    If the collection already exists and `overwrite` is `True`, it will be removed before storing
    the new documents. If `add` is `True`, the new documents will be appended to the existing
    collection.
    """

    def _extract_docs_info(docs_list: List[Document]) -> str:
        """
        Extracts and returns the names, titles, or sources from a list of documents as a string.
        """
        doc_names = set()
        for document in docs_list:
            meta = getattr(document, "metadata", {})
            doc_name = meta.get("name") or meta.get("title") or meta.get("source", "")
            if doc_name:
                doc_names.add(doc_name)
        return ", ".join(doc_names)

    log.info(f"START - save_docs_to_vector_db for collection '{collection_name}'")
    log.info(f"Input document sources: {_extract_docs_info(docs)}")
    log.info(f"Options => overwrite: {overwrite}, split: {split}, add: {add}")

    try:
        # If a collection with the same name exists, handle overwrite or add logic.
        if vector_service.has_collection(collection_name=collection_name):
            log.info(f"Collection '{collection_name}' already exists.")

            if overwrite:
                log.info(f"Overwriting: Dropping existing collection '{collection_name}'")
                vector_service.drop_collection(collection_name=collection_name)
            elif not add:
                log.info("Collection exists. Skipping save as both overwrite and add are False.")
                return True

        # Optionally split documents into smaller chunks based on token count.
        if split:
            log.info("Splitting documents using TokenTextSplitter")
            doc_splitter = TokenTextSplitter(
                encoding_name="cl100k_base",  # OpenAI GPT-4 tokenizer
                chunk_size=200,              # Number of tokens per chunk
                chunk_overlap=20,            # Overlapping tokens between chunks
                add_start_index=True         # Adds the start index of each chunk
            )
            docs = doc_splitter.split_documents(docs)
            log.info(f"Documents split into {len(docs)} chunks")

        if not docs:
            log.warning("No content found after splitting. Raising EMPTY_CONTENT error.")
            raise ValueError(ErrorMessages.EMPTY_CONTENT)

        # Prepare texts and metadata for embedding and storage.
        texts = [doc.page_content for doc in docs]
        combined_metadata = []

        log.info("Preparing metadata for each document...")
        for doc in docs:
            merged_meta = {
                **doc.metadata,
                **(metadata if metadata else {}),
                "embedding_config": json.dumps(
                    {
                        "engine": HOST_NAME,
                        "model": NOMIC_EMBEDDING_MODEL,
                    }
                ),
            }
            combined_metadata.append(merged_meta)

        log.info("Generating embeddings...")
        embedding_function = get_embedding_function(HOST_NAME, NOMIC_EMBEDDING_MODEL)
        embeddings = [embedding_function(text.replace("\n", "")) for text in texts]
        log.info(f"Generated embeddings for {len(embeddings)} chunks")

        # Build items to insert into the vector database.
        collection_items = []
        for index, text_content in enumerate(texts):
            collection_items.append(
                {
                    "id": str(uuid.uuid4()),
                    "text": text_content,
                    "vector": embeddings[index],
                    "metadata": combined_metadata[index],
                }
            )

        log.info(f"Inserting {len(collection_items)} items into collection '{collection_name}'")
        vector_service.insert(collection_name=collection_name, items=collection_items)
        log.info("INSERT SUCCESSFUL")

        return True

    except Exception as err:
        log.exception(f"FAILED to save_docs_to_vector_db: {err}")
        raise err


def save_to_vector_db(
    docs: List[Document],
    collection_name: str,
    metadata: Optional[dict] = None,
    split: bool = True,
    add: bool = True,
    chunk_size: Optional[int] = None,
) -> bool:
    """
    Saves a list of documents to a vector database collection.

    Args:
        docs: List of documents to save
        collection_name: Name of the collection to save to
        metadata: Optional metadata to add to all documents
        split: Whether to split documents into chunks
        add: Whether to add to existing collection or create new
        chunk_size: Optional chunk size for text splitting. If None, uses default from settings.

    Returns:
        bool: True if successful, raises exception otherwise
    """

    def _extract_docs_info(docs_list: List[Document]) -> str:
        """
        Extracts and returns the names, titles, or sources from a list of documents as a string.
        """
        doc_names = set()
        for document in docs_list:
            meta = getattr(document, "metadata", {})
            doc_name = meta.get("name") or meta.get("title") or meta.get("source", "")
            if doc_name:
                doc_names.add(doc_name)
        return ", ".join(doc_names)

    log.info(f"START - save_to_vector_db for collection '{collection_name}'")
    log.info(f"Input document sources: {_extract_docs_info(docs)}")
    log.info(f"Options => split: {split}, add: {add}, chunk_size: {chunk_size}")

    try:
        # If a collection with the same name exists, handle overwrite or add logic.
        if not vector_service.has_collection(collection_name=collection_name):
            log.info(f"Collection '{collection_name}' does not exist. Will be created on insert.")

        # Optionally split documents into smaller chunks based on token count.
        if split:
            # Use provided chunk_size or default from settings
            chunk_size = chunk_size or settings.DEFAULT_CHUNK_SIZE
            log.info(f"Splitting documents using chunk_size={chunk_size}")
            doc_splitter = TokenTextSplitter(
                encoding_name="cl100k_base",
                chunk_size=chunk_size,
                chunk_overlap=settings.CHUNK_OVERLAP,
                add_start_index=True
            )
            docs = doc_splitter.split_documents(docs)
            log.info(f"Documents split into {len(docs)} chunks")

        if not docs:
            log.warning("No content after splitting. Raising EMPTY_CONTENT error.")
            raise ValueError(ErrorMessages.EMPTY_CONTENT)

        # Prepare texts and metadata for embedding and storage.
        texts = [doc.page_content for doc in docs]
        combined_metadata = []

        log.info("Preparing metadata for chunks...")
        for doc in docs:
            merged_meta = {
                **doc.metadata,
                **(metadata or {}),
                "embedding_config": json.dumps({
                    "engine": HOST_NAME,
                    "model": NOMIC_EMBEDDING_MODEL,
                    "chunk_size": chunk_size
                }),
            }
            combined_metadata.append(merged_meta)

        log.info("Generating embeddings...")
        embedding_function = get_embedding_function(HOST_NAME, NOMIC_EMBEDDING_MODEL)
        embeddings = [embedding_function(text.replace("\n", "")) for text in texts]
        log.info(f"Generated embeddings for {len(embeddings)} chunks")

        # Build items to insert into the vector database.
        collection_items = []
        for index, text_content in enumerate(texts):
            collection_items.append(
                {
                    "id": str(uuid.uuid4()),
                    "text": text_content,
                    "vector": embeddings[index],
                    "metadata": combined_metadata[index],
                }
            )

        log.info(f"Upserting {len(collection_items)} items into collection '{collection_name}'")
        vector_service.upsert(collection_name=collection_name, items=collection_items)
        log.info("UPSERT SUCCESSFUL")

        return True

    except Exception as err:
        log.exception(f"FAILED to save_to_vector_db: {err}")
        raise err
