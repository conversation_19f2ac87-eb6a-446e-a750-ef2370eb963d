import asyncio
import logging
import os
import shutil
from abc import ABC, abstractmethod
from io import BytesIO
from typing import Binary<PERSON>, Tuple, List, Optional

from sqlalchemy.future import select
import aiofiles
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.core.config import settings
from app.utils.encryption import decrypt_secret

log = logging.getLogger(__name__)
UPLOAD_DIR = "/app/uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)


class StorageProvider(ABC):
    @abstractmethod
    async def get_file(self, file_path: str) -> str:
        pass

    @abstractmethod
    async def upload_file(self, file: BinaryIO, filename: Optional[str] = None, target_path: Optional[str] = None) -> \
    Tuple[bytes, str]:
        pass

    @abstractmethod
    async def delete_file(self, file_path: str) -> None:
        pass

    @abstractmethod
    async def delete_all_files(self) -> None:
        pass

    @abstractmethod
    async def delete_folder(self) -> None:
        pass


class LocalStorageProvider(StorageProvider):
    @staticmethod
    async def upload_file(file: BinaryIO, filename: Optional[str] = None, target_path: Optional[str] = None) -> Tuple[
        bytes, str]:
        contents = await asyncio.to_thread(file.read)
        if not contents:
            raise ValueError("File content is empty")
        if not target_path:
            file_path = os.path.join(UPLOAD_DIR, filename)

            async with aiofiles.open(file_path, "wb") as f:
                await f.write(contents)

            log.info(f"File uploaded successfully to {file_path}")
            return contents, file_path
        else:
            directory = os.path.dirname(target_path)
            os.makedirs(directory, exist_ok=True)
            async with aiofiles.open(target_path, "wb") as buffer:
                await buffer.write(contents)
            log.info(f"File uploaded successfully to {target_path}")
            return contents, target_path

    @staticmethod
    async def get_file(file_path: str) -> str:
        return file_path

    @staticmethod
    async def delete_file(file_path: str) -> None:
        if await asyncio.to_thread(os.path.isfile, file_path):
            await asyncio.to_thread(os.remove, file_path)
            log.info(f"File {file_path} deleted successfully.")
        else:
            log.warning(f"File {file_path} not found in local storage.")

    @staticmethod
    async def delete_all_files() -> None:
        loop = asyncio.get_running_loop()
        def _delete_files_sync() -> None:
            """
            Synchronously walks through UPLOAD_DIR and deletes all files
            and directories. This is executed in a separate thread to
            prevent blocking the event loop.
            """
            if os.path.exists(UPLOAD_DIR):
                for filename in os.listdir(UPLOAD_DIR):
                    file_path = os.path.join(UPLOAD_DIR, filename)
                    try:
                        if os.path.isfile(file_path) or os.path.islink(file_path):
                            os.unlink(file_path)
                            log.info(f"Deleted file or link: {file_path}")
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            log.info(f"Deleted directory: {file_path}")
                    except Exception as err:
                        log.error(f"Failed to delete {file_path}. Reason: {err}")
            else:
                log.warning(f"Directory '{UPLOAD_DIR}' not found.")

        # Offload the blocking file operations to a thread pool executor
        await loop.run_in_executor(None, _delete_files_sync)

    @staticmethod
    async def delete_folder(kb_directory_path: str) -> None:
        if not os.path.exists(kb_directory_path):
            log.info(f"Directory {kb_directory_path} does not exist.")
            return

        # HARD DELETE: remove entire directory tree
        shutil.rmtree(kb_directory_path)
        log.info(f"Files in '{kb_directory_path}' removed successfully.")

        pass



def get_storage_provider(storage_provider: str) -> StorageProvider:
    """
    Factory function to return the appropriate storage provider.
    """
    if storage_provider == "local":
        return LocalStorageProvider()
    else:
        raise RuntimeError(f"Unsupported storage provider: {storage_provider}")


# Initialize the storage provider
Storage: StorageProvider = get_storage_provider("local")
