import uuid
from datetime import datetime, timedelta

from sqlalchemy import <PERSON>umn, Integer, String, Boolean, DateTime, func, ForeignKey, Enum, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.database.database import Base
from app.models.enums import AuthProviderType, RoleType, PermissionLevel, ResourceType


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), index=True, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    is_super_admin = Column(Boolean, default=False, nullable=False)

    # Relationships
    auth_providers = relationship("UserAuth", back_populates="user", cascade="all, delete-orphan")
    roles = relationship("UserRole", back_populates="user", cascade="all, delete-orphan")
    chats = relationship("Chat", back_populates="user")


class UserAuth(Base):
    __tablename__ = "user_auths"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    provider = Column(Enum(AuthProviderType), nullable=False)
    provider_user_id = Column(String, nullable=True)  # e.g. Google sub or LDAP DN
    hashed_password = Column(String, nullable=True)  # only for local accounts
    extra_data = Column(JSON, nullable=True)

    # Relationships
    user = relationship("User", back_populates="auth_providers")


class UserRole(Base):
    __tablename__ = "user_roles"
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    role = Column(Enum(RoleType), nullable=False)
    resource_type = Column(Enum(ResourceType), nullable=False)
    resource_id = Column(UUID(as_uuid=True), nullable=True)
    permissions = Column(String, nullable=False)  # Simplified to string instead of array

    # Relationships
    user = relationship("User", back_populates="roles")