import os
from typing import AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, declarative_base, Session

from app.core.config import settings

# Create the asynchronous engine with future support.
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=True,  # Set to False in production.
    future=True
)

# Generate sync database URL from async URL
SYNC_DB_URL = settings.DATABASE_URL.replace("+asyncpg", "")

sync_engine = create_engine(SYNC_DB_URL, echo=False)


# Configure the asynchronous session factory
AsyncSessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False
)

# Configure the synchronous session factory
SyncSessionLocal = sessionmaker(
    bind=sync_engine,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False
)

# Base class for our ORM models.
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency function that creates and yields an asynchronous database session.

    This function can be used with dependency injection frameworks (e.g., FastAPI)
    to provide a database session per request.
    """
    async with AsyncSessionLocal() as session:
        yield session

def get_sync_db() -> Session:
    db = SyncSessionLocal()
    return db