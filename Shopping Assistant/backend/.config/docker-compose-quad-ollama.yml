services:

  qdrant:
    image: qdrant/qdrant:v1.13.4
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: always
    networks:
      - llama-net


  ollama:
    image: ollama/ollama:0.5.3
    ports:
      - "11434:11434"              
    networks:
      - llama-net
    volumes:
      - ollama_data:/root/.ollama             
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

networks:
  llama-net:
    external: true

volumes:
  qdrant_data:
  ollama_data:
  
