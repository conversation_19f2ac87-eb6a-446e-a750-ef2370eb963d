services:

  postgres:
    image: postgres:16.6-alpine3.20
    container_name: vexabot-astar-db
    env_file:
      - .env.db
    ports:
      - "5432:5432"
    networks:
      - llama-net
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  redis:
    image: redis:7.4.2-alpine3.21
    restart: always
    networks:
      - llama-net
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    volumes:
      - cache:/data

  fastapi:
    image: ${WEB_IMAGE_NAME}:${WEB_TAG}
    container_name: vexabot-astar-app
    volumes:
      - ./uploads:/app/uploads
    ports:
      - "8000:8000"
    env_file:
      - .env
    networks:
      - llama-net
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      celery_worker:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    command: ["uvicorn", "app.fastapi_app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  celery_worker:
    image: ${WEB_IMAGE_NAME}:${WEB_TAG}
    volumes:
      - ./uploads:/app/uploads
    env_file:
      - .env
    networks:
      - llama-net
    depends_on:
      - redis
      - postgres
    command: [
      "celery",
      "-A", "app.worker.celery:celery_app",
      "worker",
      "--loglevel=info",
      "--concurrency=4"
    ]
    healthcheck:
      test: ["CMD", "celery", "-A", "app.worker.celery:celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: ${NGINX_IMAGE_NAME}:${NGINX_TAG}
    container_name: vexabot-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      fastapi:
        condition: service_started
    volumes:
      - ./certbot/www/:/var/www/certbot/:ro
      - ./certbot/conf/:/etc/letsencrypt/:ro
    networks:
      - llama-net

  certbot:
    image: certbot/certbot:latest
    profiles:
      - certbot
    volumes:
      - ./certbot/www/:/var/www/certbot/:rw
      - ./certbot/conf/:/etc/letsencrypt/:rw
    networks:
      - llama-net

networks:
  llama-net:
    external: true

volumes:
  postgres_data:
  cache:

