server {
    listen 80 default_server;
    server_name _;
    server_tokens off;
    charset utf-8;
    client_max_body_size 100M;

    location / {
        proxy_pass http://fastapi:8000;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
        proxy_buffering off;
	proxy_connect_timeout       300s;
        proxy_send_timeout          300s;
        proxy_read_timeout          300s;
        send_timeout                300s;
    }
}
