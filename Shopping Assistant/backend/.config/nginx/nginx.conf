user  root;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    # Gzip Compression
    gzip on;
    # gzip_min_length 1000;
    gzip_types text/plain application/xml;
    gzip_proxied expired no-cache no-store private auth;
    gzip_vary on;

    # include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-available/*; 
}
